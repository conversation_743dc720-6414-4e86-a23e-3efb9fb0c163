version: "3.1"

intents:
  - greet
  - goodbye
  - affirm
  - deny
  - mood_great
  - mood_unhappy
  - bot_challenge
  - course_info
  - schedule_query
  - assignment_query
  - exam_query
  - lecturer_query
  - out_of_scope

entities:
  - course_code
  - lecturer_name
  - day_of_week
  - time
  - program
  - year_level

slots:
  user_name:
    type: text
    influence_conversation: false
    mappings:
    - type: from_text
      conditions:
      - active_loop: null
        requested_slot: user_name
  
  user_program:
    type: text
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: program
  
  user_year:
    type: text
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: year_level
  
  course_code:
    type: text
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: course_code
  
  lecturer_name:
    type: text
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: lecturer_name

responses:
  utter_greet:
  - text: "Hello! I'm your academic assistant. I can help you with course information, schedules, assignments, exams, and lecturer details. How can I assist you today?"
  
  utter_cheer_up:
  - text: "Here is something to cheer you up:"
    image: "https://i.imgur.com/nGF1K8f.jpg"
  
  utter_did_that_help:
  - text: "Did that help you?"
  
  utter_happy:
  - text: "Great, carry on!"
  
  utter_goodbye:
  - text: "Goodbye! Feel free to ask me anything about your academics anytime."
  - text: "See you later! I'm here whenever you need academic assistance."
  
  utter_iamabot:
  - text: "I am an AI-powered academic assistant designed to help students with their academic queries."
  
  utter_please_rephrase:
  - text: "I'm sorry, I didn't understand that. Could you please rephrase your question?"
  - text: "I'm not sure what you mean. Can you try asking in a different way?"
  
  utter_out_of_scope:
  - text: "I'm designed to help with academic queries like course information, schedules, assignments, exams, and lecturer details. Is there something academic I can help you with?"
  
  utter_course_info_help:
  - text: "I can help you with course information. Please provide a course code (e.g., CS101) or ask about courses in your program."
  
  utter_schedule_help:
  - text: "I can show you class schedules. You can ask for schedules by day (e.g., 'Monday schedule') or by course code (e.g., 'CS101 schedule')."
  
  utter_assignment_help:
  - text: "I can help with assignment information. Ask about 'upcoming assignments' or assignments for a specific course."
  
  utter_exam_help:
  - text: "I can provide exam information. Ask about 'upcoming exams' or exams for a specific course."
  
  utter_lecturer_help:
  - text: "I can provide lecturer information including contact details and office hours. Just mention the lecturer's name."

actions:
  - action_get_course_info
  - action_get_schedule
  - action_get_assignments
  - action_get_exams
  - action_get_lecturer_info
  - action_default_fallback

forms: {}

e2e_actions: []
