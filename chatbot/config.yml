# Configuration for Rasa NLU and Core models
language: en

pipeline:
  # WhitespaceTokenizer
  - name: WhitespaceTokenizer
  
  # RegexFeaturizer
  - name: RegexFeaturizer
  
  # LexicalSyntacticFeaturizer
  - name: LexicalSyntacticFeaturizer
  
  # CountVectorsFeaturizer
  - name: CountVectorsFeaturizer
    analyzer: char_wb
    min_ngram: 1
    max_ngram: 4
  
  # DIETClassifier for intent classification and entity extraction
  - name: DIETClassifier
    epochs: 100
    constrain_similarities: true
    model_confidence: softmax
  
  # EntitySynonymMapper
  - name: EntitySynonymMapper
  
  # ResponseSelector
  - name: ResponseSelector
    epochs: 100
    constrain_similarities: true
    model_confidence: softmax

# Configuration for Rasa Core
policies:
  # MemoizationPolicy
  - name: MemoizationPolicy
    max_history: 5
  
  # RulePolicy
  - name: RulePolicy
    core_fallback_threshold: 0.4
    core_fallback_action_name: "action_default_fallback"
    enable_fallback_prediction: True
  
  # UnexpecTEDIntentPolicy
  - name: UnexpecTEDIntentPolicy
    max_history: 5
    epochs: 100
    constrain_similarities: true
    model_confidence: softmax
  
  # TEDPolicy
  - name: TEDPolicy
    max_history: 5
    epochs: 100
    constrain_similarities: true
    model_confidence: softmax

# Assistant ID for analytics
assistant_id: academic_chatbot
