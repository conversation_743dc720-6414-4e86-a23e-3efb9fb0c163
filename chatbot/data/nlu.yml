version: "3.1"

nlu:
- intent: greet
  examples: |
    - hey
    - hello
    - hi
    - hello there
    - good morning
    - good evening
    - moin
    - hey there
    - let's go
    - hey dude
    - goodmorning
    - goodevening
    - good afternoon
    - hi there
    - greetings
    - hello bot
    - hi bot

- intent: goodbye
  examples: |
    - cu
    - good by
    - cee you later
    - good night
    - bye
    - goodbye
    - have a nice day
    - see you around
    - bye bye
    - see you later
    - thanks
    - thank you
    - thanks for your help
    - thank you for helping me

- intent: affirm
  examples: |
    - yes
    - y
    - indeed
    - of course
    - that sounds good
    - correct
    - yes please
    - yeah
    - yep
    - sure
    - absolutely
    - right
    - exactly

- intent: deny
  examples: |
    - no
    - n
    - never
    - I don't think so
    - don't like that
    - no way
    - not really
    - nope
    - not at all
    - definitely not

- intent: mood_great
  examples: |
    - perfect
    - great
    - amazing
    - feeling like a king
    - wonderful
    - I am feeling very good
    - I am great
    - I am amazing
    - I am going to save the world
    - super stoked
    - extremely good
    - so so perfect
    - so good
    - so perfect

- intent: mood_unhappy
  examples: |
    - my day was horrible
    - I am sad
    - I don't feel very well
    - I am disappointed
    - super sad
    - I'm so sad
    - sad
    - very sad
    - unhappy
    - not good
    - not very good
    - extremly sad
    - so saad
    - so sad

- intent: bot_challenge
  examples: |
    - are you a bot?
    - are you a human?
    - am I talking to a bot?
    - am I talking to a human?
    - what are you?
    - who are you?
    - are you real?
    - are you artificial?

- intent: course_info
  examples: |
    - tell me about [CS101](course_code)
    - what is [MATH201](course_code)?
    - course information for [ENG102](course_code)
    - describe [PHYS301](course_code)
    - I want to know about [CS101](course_code)
    - what courses are available?
    - show me course details
    - course description
    - what are the prerequisites for [CS101](course_code)?
    - how many credits is [MATH201](course_code)?
    - tell me about courses in [Computer Science](program)
    - what courses do I have in year [2](year_level)?
    - show me [Computer Science](program) courses
    - list courses for year [3](year_level)

- intent: schedule_query
  examples: |
    - what's my schedule?
    - show me my timetable
    - when is [CS101](course_code)?
    - what time is [MATH201](course_code)?
    - schedule for [Monday](day_of_week)
    - [Tuesday](day_of_week) schedule
    - what classes do I have on [Wednesday](day_of_week)?
    - when do I have classes?
    - show me [Thursday](day_of_week) timetable
    - what's on [Friday](day_of_week)?
    - class schedule
    - my timetable
    - when is my next class?

- intent: assignment_query
  examples: |
    - what assignments do I have?
    - upcoming assignments
    - assignment deadlines
    - when is my assignment due?
    - show me assignments for [CS101](course_code)
    - [MATH201](course_code) assignments
    - what's due soon?
    - assignment due dates
    - homework deadlines
    - upcoming homework
    - what assignments are due this week?
    - show me my assignments

- intent: exam_query
  examples: |
    - when are my exams?
    - exam schedule
    - upcoming exams
    - exam dates
    - when is the [CS101](course_code) exam?
    - [MATH201](course_code) exam date
    - show me exam timetable
    - final exam schedule
    - midterm dates
    - test schedule
    - when do I have tests?

- intent: lecturer_query
  examples: |
    - who teaches [CS101](course_code)?
    - lecturer for [MATH201](course_code)
    - tell me about [Dr. Smith](lecturer_name)
    - [Professor Johnson](lecturer_name) contact information
    - office hours for [Dr. Brown](lecturer_name)
    - how to contact [Prof. Davis](lecturer_name)?
    - [Dr. Wilson](lecturer_name) email
    - instructor information
    - teacher details
    - faculty contact

- intent: out_of_scope
  examples: |
    - what's the weather like?
    - tell me a joke
    - what's for lunch?
    - how old are you?
    - what's your favorite color?
    - can you help me with cooking?
    - what movies should I watch?
    - tell me about sports
    - what's the news today?
    - help me with my relationship
