version: "3.1"

stories:

- story: happy path
  steps:
  - intent: greet
  - action: utter_greet
  - intent: mood_great
  - action: utter_happy

- story: sad path 1
  steps:
  - intent: greet
  - action: utter_greet
  - intent: mood_unhappy
  - action: utter_cheer_up
  - action: utter_did_that_help
  - intent: affirm
  - action: utter_happy

- story: sad path 2
  steps:
  - intent: greet
  - action: utter_greet
  - intent: mood_unhappy
  - action: utter_cheer_up
  - action: utter_did_that_help
  - intent: deny
  - action: utter_goodbye

- story: course information flow
  steps:
  - intent: greet
  - action: utter_greet
  - intent: course_info
  - action: action_get_course_info
  - intent: goodbye
  - action: utter_goodbye

- story: schedule query flow
  steps:
  - intent: greet
  - action: utter_greet
  - intent: schedule_query
  - action: action_get_schedule
  - intent: goodbye
  - action: utter_goodbye

- story: assignment query flow
  steps:
  - intent: greet
  - action: utter_greet
  - intent: assignment_query
  - action: action_get_assignments
  - intent: goodbye
  - action: utter_goodbye

- story: exam query flow
  steps:
  - intent: greet
  - action: utter_greet
  - intent: exam_query
  - action: action_get_exams
  - intent: goodbye
  - action: utter_goodbye

- story: lecturer query flow
  steps:
  - intent: greet
  - action: utter_greet
  - intent: lecturer_query
  - action: action_get_lecturer_info
  - intent: goodbye
  - action: utter_goodbye

- story: bot challenge
  steps:
  - intent: bot_challenge
  - action: utter_iamabot

- story: out of scope
  steps:
  - intent: out_of_scope
  - action: utter_out_of_scope

- story: interactive_story_1
  steps:
  - intent: greet
  - action: utter_greet
  - intent: course_info
  - action: action_get_course_info
  - intent: schedule_query
  - action: action_get_schedule
  - intent: goodbye
  - action: utter_goodbye

- story: help with course info
  steps:
  - intent: course_info
  - action: utter_course_info_help
  - intent: course_info
  - action: action_get_course_info

- story: help with schedule
  steps:
  - intent: schedule_query
  - action: utter_schedule_help
  - intent: schedule_query
  - action: action_get_schedule

- story: help with assignments
  steps:
  - intent: assignment_query
  - action: utter_assignment_help
  - intent: assignment_query
  - action: action_get_assignments

- story: help with exams
  steps:
  - intent: exam_query
  - action: utter_exam_help
  - intent: exam_query
  - action: action_get_exams

- story: help with lecturers
  steps:
  - intent: lecturer_query
  - action: utter_lecturer_help
  - intent: lecturer_query
  - action: action_get_lecturer_info
