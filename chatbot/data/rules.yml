version: "3.1"

rules:

- rule: Say goodbye anytime the user says goodbye
  steps:
  - intent: goodbye
  - action: utter_goodbye

- rule: Say 'I am a bot' anytime the user challenges
  steps:
  - intent: bot_challenge
  - action: utter_iamabot

- rule: Handle out of scope queries
  steps:
  - intent: out_of_scope
  - action: utter_out_of_scope

- rule: Activate fallback when NLU confidence is low
  steps:
  - intent: nlu_fallback
  - action: action_default_fallback

- rule: Course information requests
  condition:
  - active_loop: null
  steps:
  - intent: course_info
  - action: action_get_course_info

- rule: Schedule query requests
  condition:
  - active_loop: null
  steps:
  - intent: schedule_query
  - action: action_get_schedule

- rule: Assignment query requests
  condition:
  - active_loop: null
  steps:
  - intent: assignment_query
  - action: action_get_assignments

- rule: Exam query requests
  condition:
  - active_loop: null
  steps:
  - intent: exam_query
  - action: action_get_exams

- rule: Lecturer query requests
  condition:
  - active_loop: null
  steps:
  - intent: lecturer_query
  - action: action_get_lecturer_info
