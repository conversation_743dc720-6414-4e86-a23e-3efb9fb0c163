#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.chat-container {
  max-width: 420px;
  margin: 40px auto;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.07);
  display: flex;
  flex-direction: column;
  height: 80vh;
  background: #fff;
}

.chat-header {
  padding: 16px;
  font-size: 1.2rem;
  font-weight: bold;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
  background: #f9f9f9;
}

.chat-history {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  background: #f7f8fa;
  position: relative;
}

.message-row {
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.message-row.user {
  flex-direction: row-reverse;
}

.avatar {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  background: #e3e3e3;
  border-radius: 50%;
}

.message-text {
  max-width: 70%;
  padding: 10px 14px;
  border-radius: 16px;
  font-size: 1rem;
  background: #fff;
  border: 1px solid #e0e0e0;
  word-break: break-word;
}

.message-row.user .message-text {
  background: #d1e7ff;
  border-color: #b6daff;
}

.typing {
  font-style: italic;
  color: #888;
}

.chat-input {
  display: flex;
  border-top: 1px solid #f0f0f0;
  padding: 12px;
  background: #fafbfc;
}

.chat-input textarea {
  flex: 1;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  outline: none;
  resize: none;
  min-height: 40px;
  max-height: 120px;
  margin-right: 8px;
}

.chat-input button {
  margin-left: 8px;
  padding: 10px 18px;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.chat-input button:hover {
  background: #125ea7;
}

.chat-input button[disabled] {
  background: #e0e0e0;
  color: #aaa;
  cursor: not-allowed;
}

.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f7f8fa;
}

.login-form {
  background: #fff;
  padding: 32px 28px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.07);
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 320px;
}

.login-form h2 {
  margin-bottom: 8px;
  font-size: 1.3rem;
  text-align: center;
}

.login-form input {
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
}

.login-form button {
  padding: 10px 0;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.login-form button:hover {
  background: #125ea7;
}

.profile-bar {
  margin-top: 8px;
  font-size: 0.95rem;
  color: #555;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.profile-btn {
  margin: 0 4px;
  padding: 4px 12px;
  background: #e3e3e3;
  color: #333;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  cursor: pointer;
  transition: background 0.2s;
}

.profile-btn:hover {
  background: #b6daff;
}

.profile-btn.logout {
  background: #ffdddd;
  color: #b71c1c;
}

.profile-btn.logout:hover {
  background: #ffb3b3;
}

.status {
  font-size: 0.75rem;
  color: #1976d2;
  margin-left: 4px;
}

.timestamp {
  font-size: 0.75rem;
  color: #aaa;
  margin-top: 2px;
  text-align: right;
}

.scroll-bottom-btn {
  position: absolute;
  right: 24px;
  bottom: 24px;
  z-index: 2;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 50px;
  padding: 8px 18px;
  font-size: 1rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
  cursor: pointer;
  transition: background 0.2s;
}

.scroll-bottom-btn:hover {
  background: #125ea7;
}
