#root {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.chat-container {
  width: 1400px;
  height: 900px;
  margin: 0;
  border: none;
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3);
  display: flex;
  flex-direction: column;
  background: #ffffff;
  overflow: hidden;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.chat-header {
  padding: 32px 48px;
  border-bottom: 1px solid rgba(0,0,0,0.08);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 100px;
  position: relative;
}

.header-title h1 {
  margin: 0;
  font-size: 2.2rem;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.connection-status {
  font-size: 0.8rem;
  margin-top: 4px;
  opacity: 0.9;
}

.connection-status.connected {
  color: #4ade80;
}

.connection-status.disconnected {
  color: #f87171;
}

.connection-status.error {
  color: #fbbf24;
}

.chat-history {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  position: relative;
  min-height: 0;
}

/* Custom Scrollbar for Desktop */
.chat-history::-webkit-scrollbar {
  width: 8px;
}

.chat-history::-webkit-scrollbar-track {
  background: rgba(0,0,0,0.05);
  border-radius: 4px;
}

.chat-history::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

.chat-history::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.message-row {
  display: flex;
  align-items: flex-start;
  padding: 16px 32px;
  margin: 0;
  width: 100%;
}

.message-row.bot {
  background: #f8f9fa;
  border-left: 4px solid #667eea;
}

.message-row.user {
  background: #e3f2fd;
  border-right: 4px solid #667eea;
  flex-direction: row-reverse;
}

.avatar {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  background: #ffffff;
  border-radius: 50%;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 2px solid #e0e0e0;
  margin: 0 16px;
}

.message-text {
  flex: 1;
  padding: 0;
  border: none;
  background: transparent;
  font-size: 1rem;
  line-height: 1.6;
  word-break: break-word;
  max-width: calc(100% - 72px);
}

.message-row.user .message-text {
  color: #1565c0;
  font-weight: 500;
}

.message-row.bot .message-text {
  color: #333333;
}

.typing {
  font-style: italic;
  color: #888;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chat-input {
  display: flex;
  border-top: 1px solid #f0f0f0;
  padding: 12px;
  background: #fafbfc;
}

.chat-input textarea {
  flex: 1;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  outline: none;
  resize: none;
  min-height: 40px;
  max-height: 120px;
  margin-right: 8px;
}

.chat-input button {
  margin-left: 8px;
  padding: 10px 18px;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.chat-input button:hover {
  background: #125ea7;
}

.chat-input button[disabled] {
  background: #e0e0e0;
  color: #aaa;
  cursor: not-allowed;
}

.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f7f8fa;
}

.login-form {
  background: #fff;
  padding: 32px 28px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.07);
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 320px;
}

.login-form h2 {
  margin-bottom: 8px;
  font-size: 1.3rem;
  text-align: center;
}

.login-form input {
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
}

.login-form button {
  padding: 10px 0;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.login-form button:hover {
  background: #125ea7;
}

.profile-bar {
  margin-top: 8px;
  font-size: 0.95rem;
  color: #555;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.profile-btn {
  margin: 0 4px;
  padding: 4px 12px;
  background: #e3e3e3;
  color: #333;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  cursor: pointer;
  transition: background 0.2s;
}

.profile-btn:hover {
  background: #b6daff;
}

.profile-btn.logout {
  background: #ffdddd;
  color: #b71c1c;
}

.profile-btn.logout:hover {
  background: #ffb3b3;
}

.status {
  font-size: 0.75rem;
  color: #1976d2;
  margin-left: 4px;
}

.timestamp {
  font-size: 0.75rem;
  color: #999;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.message-row.user .timestamp {
  justify-content: flex-end;
}

.message-row.bot .timestamp {
  justify-content: flex-start;
}

.scroll-bottom-btn {
  position: absolute;
  right: 32px;
  bottom: 32px;
  z-index: 10;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 24px;
  padding: 12px 16px;
  font-size: 0.9rem;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  cursor: pointer;
  transition: all 0.2s;
}

.scroll-bottom-btn:hover {
  background: #5a6fd8;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

/* Enhanced Profile Bar */
.profile-bar {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 1rem;
  color: rgba(255,255,255,0.95);
}

.profile-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.profile-name {
  font-weight: 600;
  font-size: 1.1rem;
}

.profile-details {
  font-size: 0.9rem;
  opacity: 0.8;
}

.profile-actions {
  display: flex;
  gap: 12px;
}

.profile-btn {
  padding: 10px 16px;
  background: rgba(255,255,255,0.15);
  color: white;
  border: 1px solid rgba(255,255,255,0.25);
  border-radius: 10px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.profile-btn:hover {
  background: rgba(255,255,255,0.25);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.profile-btn.logout {
  background: rgba(239, 68, 68, 0.8);
  border-color: rgba(239, 68, 68, 0.9);
}

.profile-btn.logout:hover {
  background: rgba(239, 68, 68, 1);
}

/* Enhanced Message Styles */
.message-content {
  line-height: 1.4;
  white-space: pre-wrap;
}

.status-sent {
  color: #666;
}

.status-delivered {
  color: #1976d2;
}

.status-error {
  color: #f44336;
}

.intent-info {
  font-size: 0.7rem;
  color: #666;
  margin-left: 8px;
  cursor: help;
}

/* Enhanced Login Form */
.login-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-form {
  max-width: 500px;
  width: 100%;
  background: rgba(255, 255, 255, 0.98);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0,0,0,0.2);
  padding: 48px;
}

.login-form h2 {
  text-align: center;
  color: #333;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 12px;
  letter-spacing: -0.5px;
}

.login-subtitle {
  text-align: center;
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 32px;
  line-height: 1.5;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 24px;
}

.form-group label {
  font-weight: 600;
  color: #333;
  font-size: 1rem;
  margin-bottom: 4px;
}

.form-group input,
.form-group select {
  padding: 16px 20px;
  border: 2px solid #e8eaed;
  border-radius: 12px;
  font-size: 1.1rem;
  transition: all 0.2s;
  background: #fafbfc;
  box-shadow: inset 0 2px 4px rgba(0,0,0,0.04);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #667eea;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), inset 0 2px 4px rgba(0,0,0,0.04);
}

.form-group input:disabled,
.form-group select:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
  opacity: 0.6;
}

.form-group button {
  padding: 16px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  margin-top: 8px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.form-group button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.form-group button:disabled {
  background: #e0e0e0;
  color: #aaa;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Typing Animation */
.typing-indicator {
  display: flex;
  gap: 4px;
  margin-bottom: 4px;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #999;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Quick Responses */
.quick-responses-toggle {
  margin: 0 0 16px 0;
  padding: 12px 16px;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.quick-responses-toggle:hover {
  background: #e0e0e0;
}

.quick-responses {
  margin: 0 0 16px 0;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.quick-responses-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
  font-size: 0.9rem;
  background: #f8f9fa;
}

.quick-responses-header button {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #666;
}

.quick-responses-list {
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.quick-response-btn {
  padding: 10px 16px;
  background: #ffffff;
  border: none;
  border-bottom: 1px solid #f0f0f0;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
  width: 100%;
}

.quick-response-btn:last-child {
  border-bottom: none;
}

.quick-response-btn:hover {
  background: #f8f9fa;
  padding-left: 20px;
}

/* Enhanced Chat Input */
.chat-input-container {
  border-top: 1px solid #e0e0e0;
  background: #ffffff;
  padding: 20px 32px;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 16px;
}

.input-wrapper textarea {
  flex: 1;
  min-height: 44px;
  max-height: 120px;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 24px;
  font-size: 1rem;
  resize: none;
  transition: all 0.2s;
  font-family: inherit;
  line-height: 1.4;
  background: #f8f9fa;
}

.input-wrapper textarea:focus {
  outline: none;
  border-color: #667eea;
  background: #ffffff;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.input-wrapper textarea:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.input-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.char-count {
  font-size: 0.7rem;
  color: #999;
}

.send-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 24px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 80px;
  height: 44px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.send-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.send-btn:disabled {
  background: #e0e0e0;
  color: #aaa;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Desktop-First Design - No Mobile Responsive */
/* This is a desktop-only application */

/* Large Desktop Optimization */
@media (min-width: 1600px) {
  .chat-container {
    width: 1600px;
    height: 1000px;
  }

  .chat-header {
    padding: 40px 60px;
    min-height: 120px;
  }

  .header-title h1 {
    font-size: 2.5rem;
  }

  .chat-history {
    padding: 48px 60px;
    gap: 32px;
  }

  .message-text {
    padding: 24px 28px;
    font-size: 1.2rem;
  }

  .input-wrapper {
    padding: 40px 60px;
  }

  .input-wrapper textarea {
    min-height: 70px;
    padding: 24px 28px;
    font-size: 1.3rem;
  }

  .send-btn {
    padding: 24px 36px;
    font-size: 1.3rem;
    min-width: 140px;
    height: 70px;
  }
}

/* Minimum Desktop Size */
@media (max-width: 1400px) {
  .chat-container {
    width: 1200px;
    height: 800px;
  }

  .chat-header {
    padding: 28px 40px;
    min-height: 90px;
  }

  .header-title h1 {
    font-size: 2rem;
  }

  .chat-history {
    padding: 32px 40px;
    gap: 20px;
  }

  .input-wrapper {
    padding: 28px 40px;
  }

  .quick-responses {
    margin: 16px 40px;
  }

  .quick-responses-toggle {
    margin: 16px 40px;
  }
}
