#root {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.chat-container {
  width: 1400px;
  height: 900px;
  margin: 0;
  border: none;
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3);
  display: flex;
  flex-direction: column;
  background: #ffffff;
  overflow: hidden;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.chat-header {
  padding: 32px 48px;
  border-bottom: 1px solid rgba(0,0,0,0.08);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 100px;
  position: relative;
}

.header-title h1 {
  margin: 0;
  font-size: 2.2rem;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.connection-status {
  font-size: 0.8rem;
  margin-top: 4px;
  opacity: 0.9;
}

.connection-status.connected {
  color: #4ade80;
}

.connection-status.disconnected {
  color: #f87171;
}

.connection-status.error {
  color: #fbbf24;
}

.chat-history {
  flex: 1;
  overflow-y: auto;
  padding: 40px 48px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  background: linear-gradient(180deg, #fafbfc 0%, #f5f7fa 100%);
  position: relative;
  min-height: 0;
}

/* Custom Scrollbar for Desktop */
.chat-history::-webkit-scrollbar {
  width: 8px;
}

.chat-history::-webkit-scrollbar-track {
  background: rgba(0,0,0,0.05);
  border-radius: 4px;
}

.chat-history::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

.chat-history::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.message-row {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  max-width: 75%;
  margin-bottom: 8px;
}

.message-row.user {
  flex-direction: row-reverse;
  align-self: flex-end;
}

.avatar {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  background: linear-gradient(135deg, #f0f2f5 0%, #e8eaed 100%);
  border-radius: 50%;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 2px solid rgba(255,255,255,0.8);
}

.message-text {
  max-width: none;
  padding: 20px 24px;
  border-radius: 20px;
  font-size: 1.1rem;
  line-height: 1.5;
  background: #ffffff;
  border: 1px solid rgba(0,0,0,0.08);
  word-break: break-word;
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
  flex: 1;
  position: relative;
}

.message-row.user .message-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.message-row.bot .message-text::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 20px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid #ffffff;
}

.message-row.user .message-text::before {
  content: '';
  position: absolute;
  right: -8px;
  top: 20px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-left: 8px solid #667eea;
}

.typing {
  font-style: italic;
  color: #888;
}

.chat-input {
  display: flex;
  border-top: 1px solid #f0f0f0;
  padding: 12px;
  background: #fafbfc;
}

.chat-input textarea {
  flex: 1;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  outline: none;
  resize: none;
  min-height: 40px;
  max-height: 120px;
  margin-right: 8px;
}

.chat-input button {
  margin-left: 8px;
  padding: 10px 18px;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.chat-input button:hover {
  background: #125ea7;
}

.chat-input button[disabled] {
  background: #e0e0e0;
  color: #aaa;
  cursor: not-allowed;
}

.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f7f8fa;
}

.login-form {
  background: #fff;
  padding: 32px 28px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.07);
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 320px;
}

.login-form h2 {
  margin-bottom: 8px;
  font-size: 1.3rem;
  text-align: center;
}

.login-form input {
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
}

.login-form button {
  padding: 10px 0;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.login-form button:hover {
  background: #125ea7;
}

.profile-bar {
  margin-top: 8px;
  font-size: 0.95rem;
  color: #555;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.profile-btn {
  margin: 0 4px;
  padding: 4px 12px;
  background: #e3e3e3;
  color: #333;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  cursor: pointer;
  transition: background 0.2s;
}

.profile-btn:hover {
  background: #b6daff;
}

.profile-btn.logout {
  background: #ffdddd;
  color: #b71c1c;
}

.profile-btn.logout:hover {
  background: #ffb3b3;
}

.status {
  font-size: 0.75rem;
  color: #1976d2;
  margin-left: 4px;
}

.timestamp {
  font-size: 0.85rem;
  color: #999;
  margin-top: 6px;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
}

.scroll-bottom-btn {
  position: absolute;
  right: 24px;
  bottom: 24px;
  z-index: 2;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 50px;
  padding: 8px 18px;
  font-size: 1rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
  cursor: pointer;
  transition: background 0.2s;
}

.scroll-bottom-btn:hover {
  background: #125ea7;
}

/* Enhanced Profile Bar */
.profile-bar {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 1rem;
  color: rgba(255,255,255,0.95);
}

.profile-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.profile-name {
  font-weight: 600;
  font-size: 1.1rem;
}

.profile-details {
  font-size: 0.9rem;
  opacity: 0.8;
}

.profile-actions {
  display: flex;
  gap: 12px;
}

.profile-btn {
  padding: 10px 16px;
  background: rgba(255,255,255,0.15);
  color: white;
  border: 1px solid rgba(255,255,255,0.25);
  border-radius: 10px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.profile-btn:hover {
  background: rgba(255,255,255,0.25);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.profile-btn.logout {
  background: rgba(239, 68, 68, 0.8);
  border-color: rgba(239, 68, 68, 0.9);
}

.profile-btn.logout:hover {
  background: rgba(239, 68, 68, 1);
}

/* Enhanced Message Styles */
.message-content {
  line-height: 1.4;
  white-space: pre-wrap;
}

.status-sent {
  color: #666;
}

.status-delivered {
  color: #1976d2;
}

.status-error {
  color: #f44336;
}

.intent-info {
  font-size: 0.7rem;
  color: #666;
  margin-left: 8px;
  cursor: help;
}

/* Enhanced Login Form */
.login-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-form {
  max-width: 500px;
  width: 100%;
  background: rgba(255, 255, 255, 0.98);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0,0,0,0.2);
  padding: 48px;
}

.login-form h2 {
  text-align: center;
  color: #333;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 12px;
  letter-spacing: -0.5px;
}

.login-subtitle {
  text-align: center;
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 32px;
  line-height: 1.5;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 24px;
}

.form-group label {
  font-weight: 600;
  color: #333;
  font-size: 1rem;
  margin-bottom: 4px;
}

.form-group input,
.form-group select {
  padding: 16px 20px;
  border: 2px solid #e8eaed;
  border-radius: 12px;
  font-size: 1.1rem;
  transition: all 0.2s;
  background: #fafbfc;
  box-shadow: inset 0 2px 4px rgba(0,0,0,0.04);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #667eea;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), inset 0 2px 4px rgba(0,0,0,0.04);
}

.form-group input:disabled,
.form-group select:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
  opacity: 0.6;
}

.form-group button {
  padding: 16px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  margin-top: 8px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.form-group button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.form-group button:disabled {
  background: #e0e0e0;
  color: #aaa;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Typing Animation */
.typing-indicator {
  display: flex;
  gap: 4px;
  margin-bottom: 4px;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #999;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Quick Responses */
.quick-responses-toggle {
  margin: 16px 48px;
  padding: 16px 24px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 20px;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.quick-responses-toggle:hover {
  background: #e0e0e0;
}

.quick-responses {
  margin: 16px 48px;
  border: 1px solid #e8eaed;
  border-radius: 20px;
  background: white;
  box-shadow: 0 8px 24px rgba(0,0,0,0.12);
  overflow: hidden;
}

.quick-responses-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f2f5;
  font-weight: 600;
  font-size: 1.1rem;
  background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
}

.quick-responses-header button {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #666;
}

.quick-responses-list {
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.quick-response-btn {
  padding: 16px 20px;
  background: #ffffff;
  border: 1px solid #f0f2f5;
  border-radius: 12px;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 1rem;
  margin: 2px 0;
}

.quick-response-btn:hover {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transform: translateX(8px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

/* Enhanced Chat Input */
.chat-input-container {
  border-top: 1px solid #e8e8e8;
  background: #fafbfc;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 24px;
  padding: 32px 48px;
  background: #ffffff;
  border-top: 1px solid rgba(0,0,0,0.08);
}

.input-wrapper textarea {
  flex: 1;
  min-height: 60px;
  max-height: 180px;
  padding: 20px 24px;
  border: 2px solid #e8eaed;
  border-radius: 20px;
  font-size: 1.2rem;
  resize: none;
  transition: all 0.2s;
  font-family: inherit;
  line-height: 1.5;
  background: #fafbfc;
  box-shadow: inset 0 2px 4px rgba(0,0,0,0.04);
}

.input-wrapper textarea:focus {
  outline: none;
  border-color: #667eea;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), inset 0 2px 4px rgba(0,0,0,0.04);
}

.input-wrapper textarea:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.input-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.char-count {
  font-size: 0.7rem;
  color: #999;
}

.send-btn {
  padding: 20px 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 120px;
  height: 60px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.send-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.send-btn:disabled {
  background: #e0e0e0;
  color: #aaa;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Desktop-First Design - No Mobile Responsive */
/* This is a desktop-only application */

/* Large Desktop Optimization */
@media (min-width: 1600px) {
  .chat-container {
    width: 1600px;
    height: 1000px;
  }

  .chat-header {
    padding: 40px 60px;
    min-height: 120px;
  }

  .header-title h1 {
    font-size: 2.5rem;
  }

  .chat-history {
    padding: 48px 60px;
    gap: 32px;
  }

  .message-text {
    padding: 24px 28px;
    font-size: 1.2rem;
  }

  .input-wrapper {
    padding: 40px 60px;
  }

  .input-wrapper textarea {
    min-height: 70px;
    padding: 24px 28px;
    font-size: 1.3rem;
  }

  .send-btn {
    padding: 24px 36px;
    font-size: 1.3rem;
    min-width: 140px;
    height: 70px;
  }
}

/* Minimum Desktop Size */
@media (max-width: 1400px) {
  .chat-container {
    width: 1200px;
    height: 800px;
  }

  .chat-header {
    padding: 28px 40px;
    min-height: 90px;
  }

  .header-title h1 {
    font-size: 2rem;
  }

  .chat-history {
    padding: 32px 40px;
    gap: 20px;
  }

  .input-wrapper {
    padding: 28px 40px;
  }

  .quick-responses {
    margin: 16px 40px;
  }

  .quick-responses-toggle {
    margin: 16px 40px;
  }
}
