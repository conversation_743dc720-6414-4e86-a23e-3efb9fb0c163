#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.chat-container {
  max-width: 1200px;
  width: 95%;
  margin: 20px auto;
  border: 1px solid #e0e0e0;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  height: 85vh;
  background: #fff;
  overflow: hidden;
}

.chat-header {
  padding: 24px 32px;
  border-bottom: 1px solid #e8e8e8;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 80px;
}

.header-title h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.connection-status {
  font-size: 0.8rem;
  margin-top: 4px;
  opacity: 0.9;
}

.connection-status.connected {
  color: #4ade80;
}

.connection-status.disconnected {
  color: #f87171;
}

.connection-status.error {
  color: #fbbf24;
}

.chat-history {
  flex: 1;
  overflow-y: auto;
  padding: 24px 32px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: #f7f8fa;
  position: relative;
}

.message-row {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  max-width: 85%;
}

.message-row.user {
  flex-direction: row-reverse;
  align-self: flex-end;
}

.avatar {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  background: #e3e3e3;
  border-radius: 50%;
  flex-shrink: 0;
}

.message-text {
  max-width: none;
  padding: 16px 20px;
  border-radius: 18px;
  font-size: 1rem;
  background: #fff;
  border: 1px solid #e0e0e0;
  word-break: break-word;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  flex: 1;
}

.message-row.user .message-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
}

.typing {
  font-style: italic;
  color: #888;
}

.chat-input {
  display: flex;
  border-top: 1px solid #f0f0f0;
  padding: 12px;
  background: #fafbfc;
}

.chat-input textarea {
  flex: 1;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  outline: none;
  resize: none;
  min-height: 40px;
  max-height: 120px;
  margin-right: 8px;
}

.chat-input button {
  margin-left: 8px;
  padding: 10px 18px;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.chat-input button:hover {
  background: #125ea7;
}

.chat-input button[disabled] {
  background: #e0e0e0;
  color: #aaa;
  cursor: not-allowed;
}

.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f7f8fa;
}

.login-form {
  background: #fff;
  padding: 32px 28px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.07);
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 320px;
}

.login-form h2 {
  margin-bottom: 8px;
  font-size: 1.3rem;
  text-align: center;
}

.login-form input {
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
}

.login-form button {
  padding: 10px 0;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.login-form button:hover {
  background: #125ea7;
}

.profile-bar {
  margin-top: 8px;
  font-size: 0.95rem;
  color: #555;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.profile-btn {
  margin: 0 4px;
  padding: 4px 12px;
  background: #e3e3e3;
  color: #333;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  cursor: pointer;
  transition: background 0.2s;
}

.profile-btn:hover {
  background: #b6daff;
}

.profile-btn.logout {
  background: #ffdddd;
  color: #b71c1c;
}

.profile-btn.logout:hover {
  background: #ffb3b3;
}

.status {
  font-size: 0.75rem;
  color: #1976d2;
  margin-left: 4px;
}

.timestamp {
  font-size: 0.85rem;
  color: #999;
  margin-top: 6px;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
}

.scroll-bottom-btn {
  position: absolute;
  right: 24px;
  bottom: 24px;
  z-index: 2;
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 50px;
  padding: 8px 18px;
  font-size: 1rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
  cursor: pointer;
  transition: background 0.2s;
}

.scroll-bottom-btn:hover {
  background: #125ea7;
}

/* Enhanced Profile Bar */
.profile-bar {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.9rem;
  color: rgba(255,255,255,0.95);
}

.profile-btn {
  padding: 6px 12px;
  background: rgba(255,255,255,0.2);
  color: white;
  border: 1px solid rgba(255,255,255,0.3);
  border-radius: 6px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s;
  backdrop-filter: blur(10px);
}

.profile-btn:hover {
  background: rgba(255,255,255,0.3);
  transform: translateY(-1px);
}

.profile-btn.logout {
  background: rgba(239, 68, 68, 0.7);
  border-color: rgba(239, 68, 68, 0.8);
}

.profile-btn.logout:hover {
  background: rgba(239, 68, 68, 0.9);
}

/* Enhanced Message Styles */
.message-content {
  line-height: 1.4;
  white-space: pre-wrap;
}

.status-sent {
  color: #666;
}

.status-delivered {
  color: #1976d2;
}

.status-error {
  color: #f44336;
}

.intent-info {
  font-size: 0.7rem;
  color: #666;
  margin-left: 8px;
  cursor: help;
}

/* Enhanced Login Form */
.login-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.login-form {
  max-width: 400px;
  width: 90%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.login-subtitle {
  text-align: center;
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.form-group input,
.form-group select {
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #667eea;
}

.form-group input:disabled,
.form-group select:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

/* Typing Animation */
.typing-indicator {
  display: flex;
  gap: 4px;
  margin-bottom: 4px;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #999;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Quick Responses */
.quick-responses-toggle {
  margin: 12px 32px;
  padding: 12px 20px;
  background: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 24px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;
}

.quick-responses-toggle:hover {
  background: #e0e0e0;
}

.quick-responses {
  margin: 12px 32px;
  border: 1px solid #e0e0e0;
  border-radius: 16px;
  background: white;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.quick-responses-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
}

.quick-responses-header button {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #666;
}

.quick-responses-list {
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.quick-response-btn {
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
}

.quick-response-btn:hover {
  background: #e9ecef;
  transform: translateX(4px);
}

/* Enhanced Chat Input */
.chat-input-container {
  border-top: 1px solid #e8e8e8;
  background: #fafbfc;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 16px;
  padding: 24px 32px;
}

.input-wrapper textarea {
  flex: 1;
  min-height: 50px;
  max-height: 150px;
  padding: 16px 20px;
  border: 2px solid #e0e0e0;
  border-radius: 16px;
  font-size: 1.1rem;
  resize: none;
  transition: border-color 0.2s;
  font-family: inherit;
  line-height: 1.4;
}

.input-wrapper textarea:focus {
  outline: none;
  border-color: #667eea;
}

.input-wrapper textarea:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.input-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.char-count {
  font-size: 0.7rem;
  color: #999;
}

.send-btn {
  padding: 16px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 16px;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 100px;
  height: 50px;
}

.send-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.send-btn:disabled {
  background: #e0e0e0;
  color: #aaa;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .chat-container {
    max-width: 900px;
    width: 95%;
  }

  .chat-history {
    padding: 20px 24px;
  }

  .input-wrapper {
    padding: 20px 24px;
  }

  .quick-responses {
    margin: 12px 24px;
  }

  .quick-responses-toggle {
    margin: 12px 24px;
  }
}

@media (max-width: 768px) {
  .chat-container {
    margin: 0;
    height: 100vh;
    border-radius: 0;
    max-width: none;
    width: 100%;
  }

  .chat-header {
    padding: 16px 20px;
    min-height: 70px;
  }

  .header-title h1 {
    font-size: 1.4rem;
  }

  .profile-bar {
    flex-direction: column;
    gap: 8px;
    font-size: 0.8rem;
  }

  .chat-history {
    padding: 16px 20px;
    gap: 12px;
  }

  .message-row {
    max-width: 95%;
    gap: 8px;
  }

  .avatar {
    width: 32px;
    height: 32px;
    font-size: 1.5rem;
  }

  .message-text {
    padding: 12px 16px;
    font-size: 0.95rem;
  }

  .input-wrapper {
    padding: 16px 20px;
    gap: 12px;
  }

  .input-wrapper textarea {
    min-height: 44px;
    padding: 12px 16px;
    font-size: 1rem;
  }

  .send-btn {
    padding: 12px 20px;
    font-size: 1rem;
    min-width: 80px;
    height: 44px;
  }

  .quick-responses {
    margin: 8px 20px;
  }

  .quick-responses-toggle {
    margin: 8px 20px;
    padding: 10px 16px;
    font-size: 0.9rem;
  }
}
