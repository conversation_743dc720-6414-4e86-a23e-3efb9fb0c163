import React, { useState, useRef, useEffect } from "react";
import "./App.css";

const USER = "user";
const BOT = "bot";
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

function Message({ sender, text, timestamp, status, intent, confidence }) {
  return (
    <div className={`message-row ${sender === USER ? "user" : "bot"}`}>
      <div className="avatar">{sender === USER ? "🧑" : "🤖"}</div>
      <div className="message-text">
        <div className="message-content">{text}</div>
        <div className="timestamp">
          {timestamp}
          {sender === USER && status && (
            <span className={`status status-${status}`}> • {status}</span>
          )}
          {sender === BOT && intent && confidence && (
            <span className="intent-info" title={`Intent: ${intent}, Confidence: ${(confidence * 100).toFixed(1)}%`}>
              🎯 {intent}
            </span>
          )}
        </div>
      </div>
    </div>
  );
}

function Profile({ profile, onEdit, onLogout }) {
  return (
    <div className="profile-bar">
      <div className="profile-info">
        <div className="profile-name">{profile.name}</div>
        <div className="profile-details">{profile.program} • Year {profile.year}</div>
      </div>
      <div className="profile-actions">
        <button className="profile-btn" onClick={onEdit}>Edit Profile</button>
        <button className="profile-btn logout" onClick={onLogout}>Logout</button>
      </div>
    </div>
  );
}

function QuickResponses({ onQuickResponse, isLoading }) {
  const [quickResponses, setQuickResponses] = useState([]);
  const [showQuickResponses, setShowQuickResponses] = useState(false);

  useEffect(() => {
    fetchQuickResponses();
  }, []);

  const fetchQuickResponses = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/chat/quick-responses`);
      const data = await response.json();
      if (data.success) {
        setQuickResponses(data.quick_responses);
      }
    } catch (error) {
      console.error('Error fetching quick responses:', error);
    }
  };

  if (!showQuickResponses) {
    return (
      <button
        className="quick-responses-toggle"
        onClick={() => setShowQuickResponses(true)}
        disabled={isLoading}
      >
        💡 Quick Questions
      </button>
    );
  }

  return (
    <div className="quick-responses">
      <div className="quick-responses-header">
        <span>💡 Quick Questions</span>
        <button onClick={() => setShowQuickResponses(false)}>✕</button>
      </div>
      <div className="quick-responses-list">
        {quickResponses.map((response, idx) => (
          <button
            key={idx}
            className="quick-response-btn"
            onClick={() => {
              onQuickResponse(response);
              setShowQuickResponses(false);
            }}
            disabled={isLoading}
          >
            {response}
          </button>
        ))}
      </div>
    </div>
  );
}

function LoginForm({ onLogin, initialProfile }) {
  const [name, setName] = useState(initialProfile?.name || "");
  const [program, setProgram] = useState(initialProfile?.program || "");
  const [year, setYear] = useState(initialProfile?.year || "");
  const [email, setEmail] = useState(initialProfile?.email || "");
  const [isLoading, setIsLoading] = useState(false);
  const [availablePrograms, setAvailablePrograms] = useState([]);

  useEffect(() => {
    fetchAvailablePrograms();
  }, []);

  const fetchAvailablePrograms = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/academic/programs`);
      const data = await response.json();
      if (data.success) {
        setAvailablePrograms(data.programs);
      }
    } catch (error) {
      console.error('Error fetching programs:', error);
      // Fallback programs
      setAvailablePrograms(['Computer Science', 'Mathematics', 'Physics', 'Engineering', 'Business']);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!name || !program || !year) return;

    setIsLoading(true);
    await onLogin({ name, program, year: parseInt(year), email });
    setIsLoading(false);
  };

  return (
    <div className="login-container">
      <form className="login-form" onSubmit={handleSubmit} aria-label="Login form">
        <h2>{initialProfile ? "Edit Profile" : "Welcome to Academic Chatbot"}</h2>
        <p className="login-subtitle">
          {initialProfile ? "Update your information" : "Please provide your details to get personalized assistance"}
        </p>

        <div className="form-group">
          <label htmlFor="name">Full Name *</label>
          <input
            id="name"
            type="text"
            placeholder="Enter your full name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
            disabled={isLoading}
          />
        </div>

        <div className="form-group">
          <label htmlFor="program">Academic Program *</label>
          <select
            id="program"
            value={program}
            onChange={(e) => setProgram(e.target.value)}
            required
            disabled={isLoading}
          >
            <option value="">Select your program</option>
            {availablePrograms.map((prog, idx) => (
              <option key={idx} value={prog}>{prog}</option>
            ))}
          </select>
        </div>

        <div className="form-group">
          <label htmlFor="year">Year Level *</label>
          <select
            id="year"
            value={year}
            onChange={(e) => setYear(e.target.value)}
            required
            disabled={isLoading}
          >
            <option value="">Select year</option>
            {[1, 2, 3, 4].map(y => (
              <option key={y} value={y}>Year {y}</option>
            ))}
          </select>
        </div>

        <div className="form-group">
          <label htmlFor="email">Email (Optional)</label>
          <input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={isLoading}
          />
        </div>

        <button type="submit" disabled={isLoading || !name || !program || !year}>
          {isLoading ? "Saving..." : (initialProfile ? "Save Changes" : "Get Started")}
        </button>
      </form>
    </div>
  );
}

function App() {
  const [profile, setProfile] = useState(null);
  const [showEdit, setShowEdit] = useState(false);
  const [messages, setMessages] = useState([
    { sender: BOT, text: "Hi! I am your academic assistant. How can I help you today?", timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) },
  ]);
  const [input, setInput] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('connected');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);
  const chatHistoryRef = useRef(null);
  const inputRef = useRef(null);

  // Check API connection on mount
  useEffect(() => {
    checkApiConnection();
  }, []);

  const checkApiConnection = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/health`);
      if (response.ok) {
        setConnectionStatus('connected');
      } else {
        setConnectionStatus('error');
      }
    } catch (error) {
      setConnectionStatus('disconnected');
    }
  };

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [isTyping, showEdit, profile]);

  // Scroll button logic
  useEffect(() => {
    const chatDiv = chatHistoryRef.current;
    if (!chatDiv) return;
    const handleScroll = () => {
      const atBottom = chatDiv.scrollHeight - chatDiv.scrollTop - chatDiv.clientHeight < 10;
      setShowScrollButton(!atBottom);
    };
    chatDiv.addEventListener("scroll", handleScroll);
    return () => chatDiv.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const sendMessage = async (messageText) => {
    if (!messageText.trim()) return;

    const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    const userMessage = { sender: USER, text: messageText, timestamp, status: "sent" };

    setMessages((msgs) => [...msgs, userMessage]);
    setIsTyping(true);
    setConnectionStatus('connected'); // Reset connection status

    try {
      // Send message to backend API
      const response = await fetch(`${API_BASE_URL}/api/chat/message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: messageText,
          user_id: profile.user_id || profile.name.replace(/\s+/g, '_').toLowerCase(),
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Update message status to delivered
      setMessages((msgs) =>
        msgs.map((msg, idx) =>
          idx === msgs.length - 1 && msg.sender === USER
            ? { ...msg, status: "delivered" }
            : msg
        )
      );

      // Add bot response
      const botResponse = {
        sender: BOT,
        text: data.success ? data.response : "I'm sorry, I encountered an error. Please try again.",
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        intent: data.intent,
        confidence: data.confidence
      };

      setMessages((msgs) => [...msgs, botResponse]);

    } catch (error) {
      console.error('Error sending message:', error);
      setConnectionStatus('error');

      // Update message status to error
      setMessages((msgs) =>
        msgs.map((msg, idx) =>
          idx === msgs.length - 1 && msg.sender === USER
            ? { ...msg, status: "error" }
            : msg
        )
      );

      // Add error response
      const errorResponse = {
        sender: BOT,
        text: "I'm sorry, I'm having trouble connecting to the server. Please check your internet connection and try again.",
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };

      setMessages((msgs) => [...msgs, errorResponse]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleSend = async (e) => {
    e.preventDefault();
    if (!input.trim() || isTyping) return;

    const messageText = input;
    setInput("");
    await sendMessage(messageText);
  };

  const handleQuickResponse = async (responseText) => {
    await sendMessage(responseText);
  };

  const handleInputKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend(e);
    }
  };

  const handleLogout = () => {
    setProfile(null);
    setMessages([
      { sender: BOT, text: "Hi! I am your academic assistant. How can I help you today?", timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) },
    ]);
    setInput("");
    setShowEdit(false);
  };

  const handleEditProfile = () => {
    setShowEdit(true);
  };

  const handleSaveProfile = async (newProfile) => {
    setIsLoading(true);
    try {
      // Generate user_id if not exists
      const user_id = newProfile.user_id || newProfile.name.replace(/\s+/g, '_').toLowerCase();
      const profileData = { ...newProfile, user_id };

      // Save profile to backend
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profileData),
      });

      const data = await response.json();

      if (data.success) {
        setProfile(data.user);
        setShowEdit(false);
        setConnectionStatus('connected');

        // Load conversation history
        await loadConversationHistory(data.user.user_id);
      } else {
        console.error('Failed to save profile:', data.message);
        // Still set profile locally as fallback
        setProfile(profileData);
        setShowEdit(false);
        setConnectionStatus('error');
      }
    } catch (error) {
      console.error('Error saving profile:', error);
      setConnectionStatus('disconnected');
      // Fallback to local storage
      setProfile(newProfile);
      setShowEdit(false);
    } finally {
      setIsLoading(false);
    }
  };

  const loadConversationHistory = async (userId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/conversation-history/${userId}?limit=5`);
      const data = await response.json();

      if (data.success && data.conversations.length > 0) {
        // Convert conversation history to message format
        const historyMessages = [];
        data.conversations.reverse().forEach(conv => {
          historyMessages.push({
            sender: USER,
            text: conv.user_message,
            timestamp: new Date(conv.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            status: "delivered"
          });
          historyMessages.push({
            sender: BOT,
            text: conv.bot_response,
            timestamp: new Date(conv.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            intent: conv.intent,
            confidence: conv.confidence
          });
        });

        // Add welcome message and history
        setMessages([
          {
            sender: BOT,
            text: `Welcome back, ${profile?.name || 'there'}! I found your previous conversations. How can I help you today?`,
            timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
          },
          ...historyMessages
        ]);
      } else {
        // No history, show welcome message
        setMessages([
          {
            sender: BOT,
            text: `Hello ${profile?.name || 'there'}! I'm your academic assistant. I can help you with course information, schedules, assignments, exams, and lecturer details. What would you like to know?`,
            timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
          }
        ]);
      }
    } catch (error) {
      console.error('Error loading conversation history:', error);
      // Fallback welcome message
      setMessages([
        {
          sender: BOT,
          text: `Hello! I'm your academic assistant. How can I help you today?`,
          timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        }
      ]);
    }
  };

  if (!profile || showEdit) {
    return <LoginForm onLogin={showEdit ? handleSaveProfile : handleSaveProfile} initialProfile={profile} />;
  }

  return (
    <div className="chat-container">
      <div className="chat-header">
        <div className="header-title">
          <h1>Academic Support Chatbot</h1>
          <div className={`connection-status ${connectionStatus}`}>
            {connectionStatus === 'connected' && <span>🟢 Connected</span>}
            {connectionStatus === 'disconnected' && <span>🔴 Disconnected</span>}
            {connectionStatus === 'error' && <span>🟡 Connection Issues</span>}
          </div>
        </div>
        <Profile profile={profile} onEdit={handleEditProfile} onLogout={handleLogout} />
      </div>

      <div className="chat-history" ref={chatHistoryRef}>
        {messages.map((msg, idx) => (
          <Message
            key={idx}
            sender={msg.sender}
            text={msg.text}
            timestamp={msg.timestamp}
            status={msg.status}
            intent={msg.intent}
            confidence={msg.confidence}
          />
        ))}
        {isTyping && (
          <div className="message-row bot">
            <div className="avatar">🤖</div>
            <div className="message-text typing">
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <div>Thinking...</div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
        {showScrollButton && (
          <button className="scroll-bottom-btn" onClick={scrollToBottom} aria-label="Scroll to latest message">
            ↓ New messages
          </button>
        )}
      </div>

      <div className="chat-input-container">
        <QuickResponses onQuickResponse={handleQuickResponse} isLoading={isTyping} />

        <form className="chat-input" onSubmit={handleSend} aria-label="Chat input form">
          <div className="input-wrapper">
            <textarea
              ref={inputRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleInputKeyDown}
              placeholder="Ask me about courses, schedules, assignments, exams, or lecturers..."
              aria-label="Type your message"
              rows={1}
              disabled={isTyping}
              maxLength={500}
            />
            <div className="input-actions">
              <span className="char-count">{input.length}/500</span>
              <button
                type="submit"
                disabled={!input.trim() || isTyping}
                aria-label="Send message"
                className="send-btn"
              >
                {isTyping ? "..." : "Send"}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}

export default App;
