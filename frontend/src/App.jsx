import React, { useState, useRef, useEffect } from "react";
import "./App.css";

const USER = "user";
const BOT = "bot";

function Message({ sender, text, timestamp, status }) {
  return (
    <div className={`message-row ${sender === USER ? "user" : "bot"}`}>
      <div className="avatar">{sender === USER ? "🧑" : "🤖"}</div>
      <div className="message-text">
        <div>{text}</div>
        <div className="timestamp">
          {timestamp}
          {sender === USER && status && (
            <span className="status"> • {status}</span>
          )}
        </div>
      </div>
    </div>
  );
}

function Profile({ profile, onEdit, onLogout }) {
  return (
    <div className="profile-bar">
      <span>👤 {profile.name} ({profile.program}, Year {profile.year})</span>
      <button className="profile-btn" onClick={onEdit}>Edit Profile</button>
      <button className="profile-btn logout" onClick={onLogout}>Logout</button>
    </div>
  );
}

function LoginForm({ onLogin, initialProfile }) {
  const [name, setName] = useState(initialProfile?.name || "");
  const [program, setProgram] = useState(initialProfile?.program || "");
  const [year, setYear] = useState(initialProfile?.year || "");

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!name || !program || !year) return;
    onLogin({ name, program, year });
  };

  return (
    <div className="login-container">
      <form className="login-form" onSubmit={handleSubmit} aria-label="Login form">
        <h2>{initialProfile ? "Edit Profile" : "Login to Academic Chatbot"}</h2>
        <input
          type="text"
          placeholder="Name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          aria-label="Name"
        />
        <input
          type="text"
          placeholder="Program"
          value={program}
          onChange={(e) => setProgram(e.target.value)}
          aria-label="Program"
        />
        <input
          type="number"
          placeholder="Year"
          value={year}
          onChange={(e) => setYear(e.target.value)}
          min="1"
          max="8"
          aria-label="Year"
        />
        <button type="submit">{initialProfile ? "Save" : "Login"}</button>
      </form>
    </div>
  );
}

function App() {
  const [profile, setProfile] = useState(null);
  const [showEdit, setShowEdit] = useState(false);
  const [messages, setMessages] = useState([
    { sender: BOT, text: "Hi! I am your academic assistant. How can I help you today?", timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) },
  ]);
  const [input, setInput] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const messagesEndRef = useRef(null);
  const chatHistoryRef = useRef(null);
  const inputRef = useRef(null);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [isTyping, showEdit, profile]);

  // Scroll button logic
  useEffect(() => {
    const chatDiv = chatHistoryRef.current;
    if (!chatDiv) return;
    const handleScroll = () => {
      const atBottom = chatDiv.scrollHeight - chatDiv.scrollTop - chatDiv.clientHeight < 10;
      setShowScrollButton(!atBottom);
    };
    chatDiv.addEventListener("scroll", handleScroll);
    return () => chatDiv.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleSend = async (e) => {
    e.preventDefault();
    if (!input.trim()) return;

    const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    const userMessage = { sender: USER, text: input, timestamp, status: "sent" };

    setMessages((msgs) => [...msgs, userMessage]);
    setInput("");
    setIsTyping(true);

    try {
      // Send message to backend API
      const response = await fetch('http://localhost:5000/api/chat/message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: input,
          user_id: profile.user_id || profile.name.replace(/\s+/g, '_').toLowerCase(),
        }),
      });

      const data = await response.json();

      // Update message status to delivered
      setMessages((msgs) =>
        msgs.map((msg, idx) =>
          idx === msgs.length - 1 && msg.sender === USER
            ? { ...msg, status: "delivered" }
            : msg
        )
      );

      // Add bot response
      const botResponse = {
        sender: BOT,
        text: data.success ? data.response : "I'm sorry, I encountered an error. Please try again.",
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        intent: data.intent,
        confidence: data.confidence
      };

      setMessages((msgs) => [...msgs, botResponse]);

    } catch (error) {
      console.error('Error sending message:', error);

      // Update message status to error
      setMessages((msgs) =>
        msgs.map((msg, idx) =>
          idx === msgs.length - 1 && msg.sender === USER
            ? { ...msg, status: "error" }
            : msg
        )
      );

      // Add error response
      const errorResponse = {
        sender: BOT,
        text: "I'm sorry, I'm having trouble connecting to the server. Please check your internet connection and try again.",
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };

      setMessages((msgs) => [...msgs, errorResponse]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleInputKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend(e);
    }
  };

  const handleLogout = () => {
    setProfile(null);
    setMessages([
      { sender: BOT, text: "Hi! I am your academic assistant. How can I help you today?", timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) },
    ]);
    setInput("");
    setShowEdit(false);
  };

  const handleEditProfile = () => {
    setShowEdit(true);
  };

  const handleSaveProfile = async (newProfile) => {
    try {
      // Generate user_id if not exists
      const user_id = newProfile.user_id || newProfile.name.replace(/\s+/g, '_').toLowerCase();
      const profileData = { ...newProfile, user_id };

      // Save profile to backend
      const response = await fetch('http://localhost:5000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profileData),
      });

      const data = await response.json();

      if (data.success) {
        setProfile(data.user);
        setShowEdit(false);

        // Load conversation history
        loadConversationHistory(data.user.user_id);
      } else {
        console.error('Failed to save profile:', data.message);
        // Still set profile locally as fallback
        setProfile(profileData);
        setShowEdit(false);
      }
    } catch (error) {
      console.error('Error saving profile:', error);
      // Fallback to local storage
      setProfile(newProfile);
      setShowEdit(false);
    }
  };

  const loadConversationHistory = async (userId) => {
    try {
      const response = await fetch(`http://localhost:5000/api/auth/conversation-history/${userId}?limit=10`);
      const data = await response.json();

      if (data.success && data.conversations.length > 0) {
        // Convert conversation history to message format
        const historyMessages = [];
        data.conversations.reverse().forEach(conv => {
          historyMessages.push({
            sender: USER,
            text: conv.user_message,
            timestamp: new Date(conv.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            status: "delivered"
          });
          historyMessages.push({
            sender: BOT,
            text: conv.bot_response,
            timestamp: new Date(conv.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            intent: conv.intent
          });
        });

        // Add welcome message and history
        setMessages([
          { sender: BOT, text: `Welcome back, ${profile?.name || 'there'}! I'm your academic assistant. How can I help you today?`, timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) },
          ...historyMessages
        ]);
      }
    } catch (error) {
      console.error('Error loading conversation history:', error);
    }
  };

  if (!profile || showEdit) {
    return <LoginForm onLogin={showEdit ? handleSaveProfile : setProfile} initialProfile={profile} />;
  }

  return (
    <div className="chat-container">
      <div className="chat-header">
        Academic Support Chatbot
        <Profile profile={profile} onEdit={handleEditProfile} onLogout={handleLogout} />
      </div>
      <div className="chat-history" ref={chatHistoryRef}>
        {messages.map((msg, idx) => (
          <Message key={idx} sender={msg.sender} text={msg.text} timestamp={msg.timestamp} status={msg.status} />
        ))}
        {isTyping && (
          <div className="message-row bot">
            <div className="avatar">🤖</div>
            <div className="message-text typing">Typing...</div>
          </div>
        )}
        <div ref={messagesEndRef} />
        {showScrollButton && (
          <button className="scroll-bottom-btn" onClick={scrollToBottom} aria-label="Scroll to latest message">
            ↓ Scroll to bottom
          </button>
        )}
      </div>
      <form className="chat-input" onSubmit={handleSend} aria-label="Chat input form">
        <textarea
          ref={inputRef}
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleInputKeyDown}
          placeholder="Type your message..."
          aria-label="Type your message"
          rows={1}
        />
        <button type="submit" disabled={!input.trim()} aria-label="Send message">Send</button>
      </form>
    </div>
  );
}

export default App;
