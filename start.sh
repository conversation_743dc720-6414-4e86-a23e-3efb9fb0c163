#!/bin/bash

# AI Academic Chatbot Startup Script
# This script sets up and starts the full-stack application

echo "🚀 Starting AI Academic Chatbot Setup..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed. Please install Python 3.8 or higher."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 16 or higher."
    exit 1
fi

# Check if MongoDB is running
if ! pgrep -x "mongod" > /dev/null; then
    print_warning "MongoDB doesn't appear to be running. Please start MongoDB first."
    print_status "On macOS: brew services start mongodb-community"
    print_status "On Ubuntu: sudo systemctl start mongod"
    print_status "On Windows: net start MongoDB"
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    print_status "Creating .env file from template..."
    cp .env.example .env
    print_success ".env file created. Please review and update the configuration."
fi

# Backend setup
print_status "Setting up backend..."
cd backend

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    print_status "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source venv/bin/activate

# Install Python dependencies
print_status "Installing Python dependencies..."
pip install -r requirements.txt

# Download spaCy model
print_status "Downloading spaCy English model..."
python -m spacy download en_core_web_sm

# Seed database
print_status "Seeding database with sample data..."
python seed_database.py

print_success "Backend setup completed!"

# Frontend setup
print_status "Setting up frontend..."
cd ../frontend

# Install Node.js dependencies
print_status "Installing Node.js dependencies..."
npm install

print_success "Frontend setup completed!"

# Go back to root directory
cd ..

print_success "🎉 Setup completed successfully!"
echo ""
print_status "To start the application:"
echo "1. Backend: cd backend && source venv/bin/activate && python run.py"
echo "2. Frontend: cd frontend && npm run dev"
echo ""
print_status "Or use the package.json scripts:"
echo "npm run dev:backend  # Start backend only"
echo "npm run dev:frontend # Start frontend only"
echo "npm run dev          # Start both (requires concurrently)"
echo ""
print_status "Access the application at: http://localhost:5173"
print_status "API documentation at: http://localhost:5000"
