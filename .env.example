# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Database Configuration
MONGO_URI=mongodb://localhost:27017/academic_chatbot

# Rasa Configuration
RASA_SERVER_URL=http://localhost:5005

# CORS Configuration
CORS_ORIGINS=http://localhost:5173,http://localhost:3000

# Logging
LOG_LEVEL=INFO

# Conversation Settings
MAX_CONVERSATION_HISTORY=50

# Production Settings (for deployment)
# FLASK_ENV=production
# MONGO_URI=mongodb://your-production-mongodb-uri
# SECRET_KEY=your-production-secret-key
