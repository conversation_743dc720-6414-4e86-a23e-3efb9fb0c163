# AI-Powered Academic Support Chatbot

A full-stack, AI-powered chatbot web application designed to assist students with academic-related queries such as course information, schedules, assignments, and lecturer contacts. The chatbot supports natural language processing (NLP) and maintains conversational context.

## 🚀 Features

### Chatbot Capabilities
- ✅ Natural language understanding and response generation
- ✅ Academic intent recognition (course info, schedules, assignments, exams, lecturers)
- ✅ Entity extraction (course codes, lecturer names, dates, times)
- ✅ Conversational context maintenance
- ✅ Fallback responses for unknown queries
- ✅ Small talk support (greetings, goodbyes)

### Database Integration
- ✅ MongoDB for storing academic data and conversation logs
- ✅ User profile management (name, program, year)
- ✅ Conversation history tracking
- ✅ Academic data storage (courses, schedules, assignments, exams, lecturers)

### Web Frontend
- ✅ Responsive React.js chat interface
- ✅ Real-time message exchange
- ✅ User authentication and profile management
- ✅ Message history and status indicators
- ✅ Typing animations and visual feedback
- ✅ Scroll-to-bottom functionality

### Backend API
- ✅ Flask REST API with CORS support
- ✅ Authentication and user management endpoints
- ✅ Chat message processing and response generation
- ✅ Academic data retrieval endpoints
- ✅ Conversation logging and analytics

## 🛠 Technology Stack

### NLP/Chatbot
- **Rasa**: Intent recognition and dialogue management
- **spaCy**: Natural language entity extraction
- **Rule-based fallback**: Custom response generation system

### Backend
- **Flask**: Python web framework for REST API
- **MongoDB**: NoSQL database for data storage
- **PyMongo**: MongoDB driver for Python

### Frontend
- **React.js**: Dynamic and responsive user interface
- **Vite**: Fast build tool and development server
- **CSS3**: Modern styling with responsive design

## 📁 Project Structure

```
ai-chat-2/
├── frontend/                 # React.js frontend
│   ├── src/
│   │   ├── App.jsx          # Main chat component
│   │   ├── App.css          # Styling
│   │   └── main.jsx         # Entry point
│   ├── package.json         # Frontend dependencies
│   └── vite.config.js       # Vite configuration
├── backend/                 # Flask backend API
│   ├── app.py              # Main Flask application
│   ├── config.py           # Configuration settings
│   ├── database.py         # Database connection and operations
│   ├── models/             # Data models
│   │   ├── user.py         # User and conversation models
│   │   └── academic.py     # Academic data models
│   ├── routes/             # API route handlers
│   │   ├── auth.py         # Authentication routes
│   │   ├── chat.py         # Chat message routes
│   │   └── academic.py     # Academic data routes
│   ├── services/           # Business logic services
│   │   ├── nlp_service.py  # NLP processing
│   │   ├── rasa_service.py # Rasa integration
│   │   └── response_generator.py # Response generation
│   ├── data/               # Sample data
│   │   └── sample_data.py  # Academic sample data
│   ├── requirements.txt    # Python dependencies
│   └── seed_database.py    # Database seeding script
├── chatbot/                # Rasa chatbot configuration
│   ├── config.yml          # Rasa pipeline configuration
│   ├── domain.yml          # Intents, entities, responses
│   └── data/               # Training data
│       ├── nlu.yml         # Intent training examples
│       ├── stories.yml     # Conversation flows
│       └── rules.yml       # Conversation rules
└── README.md               # This file
```

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Node.js 16+
- MongoDB 4.4+
- Git

### 1. Clone the Repository
```bash
git clone <repository-url>
cd ai-chat-2
```

### 2. Backend Setup

#### Install Python Dependencies
```bash
cd backend
pip install -r requirements.txt
```

#### Install spaCy Language Model
```bash
python -m spacy download en_core_web_sm
```

#### Set Up Environment Variables
```bash
cp ../.env.example .env
# Edit .env with your configuration
```

#### Start MongoDB
Make sure MongoDB is running on your system:
```bash
# On macOS with Homebrew
brew services start mongodb-community

# On Ubuntu/Debian
sudo systemctl start mongod

# On Windows
net start MongoDB
```

#### Seed the Database
```bash
python seed_database.py
```

#### Start the Backend Server
```bash
python run.py
```
The API will be available at `http://localhost:5000`

### 3. Frontend Setup

#### Install Node.js Dependencies
```bash
cd frontend
npm install
```

#### Start the Development Server
```bash
npm run dev
```
The frontend will be available at `http://localhost:5173`

### 4. Optional: Rasa Setup

#### Install Rasa (Optional)
```bash
pip install rasa
```

#### Train Rasa Model
```bash
cd chatbot
rasa train
```

#### Start Rasa Server
```bash
rasa run --enable-api --cors "*"
```
Rasa will be available at `http://localhost:5005`

## 🎯 Usage

1. **Access the Application**: Open `http://localhost:5173` in your browser
2. **Create Profile**: Enter your name, program, and year level
3. **Start Chatting**: Ask questions about:
   - Course information: "Tell me about CS101"
   - Schedules: "What's my Monday schedule?"
   - Assignments: "Show me upcoming assignments"
   - Exams: "When are my exams?"
   - Lecturers: "Who teaches MATH201?"

## 📝 API Endpoints

### Authentication
- `POST /api/auth/login` - User login/registration
- `GET /api/auth/profile/{user_id}` - Get user profile
- `PUT /api/auth/profile/{user_id}` - Update user profile
- `GET /api/auth/conversation-history/{user_id}` - Get conversation history

### Chat
- `POST /api/chat/message` - Send chat message
- `POST /api/chat/feedback` - Submit feedback
- `GET /api/chat/context/{user_id}` - Get conversation context
- `GET /api/chat/quick-responses` - Get suggested responses

### Academic Data
- `GET /api/academic/courses` - Get courses
- `GET /api/academic/schedules` - Get schedules
- `GET /api/academic/assignments` - Get assignments
- `GET /api/academic/exams` - Get exams
- `GET /api/academic/lecturers` - Get lecturer information
- `GET /api/academic/programs` - Get available programs

## 🧪 Testing

### Backend Testing
```bash
cd backend
python -m pytest tests/
```

### Frontend Testing
```bash
cd frontend
npm test
```

## 🚀 Deployment

### Backend Deployment
1. Set production environment variables
2. Use a production WSGI server like Gunicorn:
```bash
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### Frontend Deployment
```bash
cd frontend
npm run build
# Deploy the dist/ folder to your web server
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit your changes: `git commit -am 'Add feature'`
4. Push to the branch: `git push origin feature-name`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

If you encounter any issues or have questions:
1. Check the troubleshooting section below
2. Search existing issues on GitHub
3. Create a new issue with detailed information

## 🔧 Troubleshooting

### Common Issues

**MongoDB Connection Error**
- Ensure MongoDB is running
- Check the MONGO_URI in your .env file
- Verify database permissions

**Frontend API Connection Error**
- Ensure backend server is running on port 5000
- Check CORS configuration
- Verify API endpoints are accessible

**Rasa Server Not Available**
- The system works without Rasa using fallback responses
- Install and train Rasa model if you want full NLP capabilities
- Check Rasa server is running on port 5005

**spaCy Model Not Found**
- Run: `python -m spacy download en_core_web_sm`
- The system will use a blank model as fallback

## 🎉 Acknowledgments

- Rasa for the conversational AI framework
- spaCy for natural language processing
- MongoDB for flexible data storage
- React.js for the interactive frontend
- Flask for the lightweight backend API
