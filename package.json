{"name": "ai-academic-chatbot", "version": "1.0.0", "description": "AI-Powered Academic Support Chatbot - Full-stack application with React frontend and Flask backend", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && python run.py", "build": "cd frontend && npm run build", "seed": "cd backend && python seed_database.py", "install:all": "npm install && cd frontend && npm install && cd ../backend && pip install -r requirements.txt", "start": "cd backend && python run.py", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["chatbot", "ai", "academic", "education", "nlp", "rasa", "flask", "react", "mongodb"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=16.0.0", "python": ">=3.8.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/ai-academic-chatbot.git"}, "bugs": {"url": "https://github.com/yourusername/ai-academic-chatbot/issues"}, "homepage": "https://github.com/yourusername/ai-academic-chatbot#readme"}