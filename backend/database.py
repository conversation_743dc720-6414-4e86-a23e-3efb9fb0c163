from flask_pymongo import PyMongo
from datetime import datetime
import logging

mongo = PyMongo()

def init_db(app):
    """Initialize database connection"""
    mongo.init_app(app)
    return mongo

class DatabaseManager:
    """Database operations manager"""
    
    @staticmethod
    def get_user_profile(user_id):
        """Get user profile by ID"""
        try:
            return mongo.db.users.find_one({"user_id": user_id})
        except Exception as e:
            logging.error(f"Error getting user profile: {e}")
            return None
    
    @staticmethod
    def create_or_update_user(user_data):
        """Create or update user profile"""
        try:
            user_data['updated_at'] = datetime.utcnow()
            result = mongo.db.users.update_one(
                {"user_id": user_data.get('user_id')},
                {"$set": user_data},
                upsert=True
            )
            return result.acknowledged
        except Exception as e:
            logging.error(f"Error creating/updating user: {e}")
            return False
    
    @staticmethod
    def save_conversation(user_id, message, response, intent=None, confidence=None):
        """Save conversation to database"""
        try:
            conversation_data = {
                "user_id": user_id,
                "user_message": message,
                "bot_response": response,
                "intent": intent,
                "confidence": confidence,
                "timestamp": datetime.utcnow()
            }
            result = mongo.db.conversations.insert_one(conversation_data)
            return str(result.inserted_id)
        except Exception as e:
            logging.error(f"Error saving conversation: {e}")
            return None
    
    @staticmethod
    def get_conversation_history(user_id, limit=20):
        """Get conversation history for a user"""
        try:
            conversations = mongo.db.conversations.find(
                {"user_id": user_id}
            ).sort("timestamp", -1).limit(limit)
            return list(conversations)
        except Exception as e:
            logging.error(f"Error getting conversation history: {e}")
            return []
    
    @staticmethod
    def get_academic_data(query_type, filters=None):
        """Get academic data based on query type"""
        try:
            collection_map = {
                'courses': 'courses',
                'schedules': 'schedules',
                'lecturers': 'lecturers',
                'assignments': 'assignments',
                'exams': 'exams'
            }
            
            collection_name = collection_map.get(query_type)
            if not collection_name:
                return []
            
            collection = getattr(mongo.db, collection_name)
            query = filters or {}
            return list(collection.find(query))
        except Exception as e:
            logging.error(f"Error getting academic data: {e}")
            return []
    
    @staticmethod
    def search_courses(search_term, program=None):
        """Search courses by name or code"""
        try:
            query = {
                "$or": [
                    {"name": {"$regex": search_term, "$options": "i"}},
                    {"code": {"$regex": search_term, "$options": "i"}},
                    {"description": {"$regex": search_term, "$options": "i"}}
                ]
            }
            
            if program:
                query["program"] = program
            
            return list(mongo.db.courses.find(query))
        except Exception as e:
            logging.error(f"Error searching courses: {e}")
            return []
    
    @staticmethod
    def get_lecturer_info(lecturer_name):
        """Get lecturer information"""
        try:
            return mongo.db.lecturers.find_one(
                {"name": {"$regex": lecturer_name, "$options": "i"}}
            )
        except Exception as e:
            logging.error(f"Error getting lecturer info: {e}")
            return None
