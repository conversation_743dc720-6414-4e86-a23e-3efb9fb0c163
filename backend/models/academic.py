from datetime import datetime
from typing import Dict, Any, List, Optional

class Course:
    """Course model for academic courses"""
    
    def __init__(self, code: str, name: str, description: str, credits: int, 
                 program: str, year: int, semester: str):
        self.code = code
        self.name = name
        self.description = description
        self.credits = credits
        self.program = program
        self.year = year
        self.semester = semester
        self.prerequisites = []
        self.lecturer = None
        self.schedule = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert course object to dictionary"""
        return {
            "code": self.code,
            "name": self.name,
            "description": self.description,
            "credits": self.credits,
            "program": self.program,
            "year": self.year,
            "semester": self.semester,
            "prerequisites": self.prerequisites,
            "lecturer": self.lecturer,
            "schedule": self.schedule
        }

class Lecturer:
    """Lecturer model for faculty information"""
    
    def __init__(self, name: str, email: str, department: str, office: str = None):
        self.name = name
        self.email = email
        self.department = department
        self.office = office
        self.phone = None
        self.office_hours = []
        self.courses = []
        self.specializations = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert lecturer object to dictionary"""
        return {
            "name": self.name,
            "email": self.email,
            "department": self.department,
            "office": self.office,
            "phone": self.phone,
            "office_hours": self.office_hours,
            "courses": self.courses,
            "specializations": self.specializations
        }

class Assignment:
    """Assignment model for coursework"""
    
    def __init__(self, title: str, course_code: str, due_date: datetime, 
                 description: str = None):
        self.title = title
        self.course_code = course_code
        self.due_date = due_date
        self.description = description
        self.type = "assignment"  # assignment, project, quiz, etc.
        self.weight = None  # percentage of final grade
        self.status = "active"  # active, completed, overdue
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert assignment object to dictionary"""
        return {
            "title": self.title,
            "course_code": self.course_code,
            "due_date": self.due_date,
            "description": self.description,
            "type": self.type,
            "weight": self.weight,
            "status": self.status
        }

class Schedule:
    """Schedule model for class timetables"""
    
    def __init__(self, course_code: str, day: str, start_time: str, 
                 end_time: str, room: str, type: str = "lecture"):
        self.course_code = course_code
        self.day = day  # Monday, Tuesday, etc.
        self.start_time = start_time  # HH:MM format
        self.end_time = end_time
        self.room = room
        self.type = type  # lecture, tutorial, lab, etc.
        self.lecturer = None
        self.recurring = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert schedule object to dictionary"""
        return {
            "course_code": self.course_code,
            "day": self.day,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "room": self.room,
            "type": self.type,
            "lecturer": self.lecturer,
            "recurring": self.recurring
        }

class Exam:
    """Exam model for examination schedules"""
    
    def __init__(self, course_code: str, date: datetime, start_time: str, 
                 duration: int, venue: str):
        self.course_code = course_code
        self.date = date
        self.start_time = start_time
        self.duration = duration  # in minutes
        self.venue = venue
        self.type = "final"  # midterm, final, quiz
        self.instructions = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exam object to dictionary"""
        return {
            "course_code": self.course_code,
            "date": self.date,
            "start_time": self.start_time,
            "duration": self.duration,
            "venue": self.venue,
            "type": self.type,
            "instructions": self.instructions
        }
