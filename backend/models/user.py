from datetime import datetime
from typing import Dict, Any, Optional

class User:
    """User model for student profiles"""
    
    def __init__(self, user_id: str, name: str, program: str, year: int, email: str = None):
        self.user_id = user_id
        self.name = name
        self.program = program
        self.year = year
        self.email = email
        self.created_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        self.preferences = {}
        self.conversation_count = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert user object to dictionary"""
        return {
            "user_id": self.user_id,
            "name": self.name,
            "program": self.program,
            "year": self.year,
            "email": self.email,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "preferences": self.preferences,
            "conversation_count": self.conversation_count
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'User':
        """Create user object from dictionary"""
        user = cls(
            user_id=data.get('user_id'),
            name=data.get('name'),
            program=data.get('program'),
            year=data.get('year'),
            email=data.get('email')
        )
        user.created_at = data.get('created_at', datetime.utcnow())
        user.updated_at = data.get('updated_at', datetime.utcnow())
        user.preferences = data.get('preferences', {})
        user.conversation_count = data.get('conversation_count', 0)
        return user
    
    def update_preferences(self, preferences: Dict[str, Any]):
        """Update user preferences"""
        self.preferences.update(preferences)
        self.updated_at = datetime.utcnow()
    
    def increment_conversation_count(self):
        """Increment conversation count"""
        self.conversation_count += 1
        self.updated_at = datetime.utcnow()

class Conversation:
    """Conversation model for chat history"""
    
    def __init__(self, user_id: str, user_message: str, bot_response: str, 
                 intent: str = None, confidence: float = None):
        self.user_id = user_id
        self.user_message = user_message
        self.bot_response = bot_response
        self.intent = intent
        self.confidence = confidence
        self.timestamp = datetime.utcnow()
        self.feedback = None  # User feedback on response quality
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert conversation object to dictionary"""
        return {
            "user_id": self.user_id,
            "user_message": self.user_message,
            "bot_response": self.bot_response,
            "intent": self.intent,
            "confidence": self.confidence,
            "timestamp": self.timestamp,
            "feedback": self.feedback
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Conversation':
        """Create conversation object from dictionary"""
        conv = cls(
            user_id=data.get('user_id'),
            user_message=data.get('user_message'),
            bot_response=data.get('bot_response'),
            intent=data.get('intent'),
            confidence=data.get('confidence')
        )
        conv.timestamp = data.get('timestamp', datetime.utcnow())
        conv.feedback = data.get('feedback')
        return conv
