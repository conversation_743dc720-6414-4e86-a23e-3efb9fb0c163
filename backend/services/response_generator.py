from database import DatabaseManager
from services.nlp_service import NLPService
from datetime import datetime, timedelta
import logging
from typing import Dict, Any, List

class ResponseGenerator:
    """Generate responses for academic queries using rule-based system"""
    
    def __init__(self):
        self.nlp_service = NLPService()
    
    def generate_response(self, message: str, user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate appropriate response based on message and context"""
        try:
            # Extract context from message
            context = self.nlp_service.extract_academic_context(message, user_context)
            intent = context['intent']
            entities = context['entities']
            query_type = context['query_type']
            
            # Generate response based on intent and query type
            if intent == 'greeting':
                response = self._generate_greeting_response(user_context)
            elif intent == 'goodbye':
                response = self._generate_goodbye_response()
            elif intent == 'course_info':
                response = self._generate_course_info_response(entities, query_type, user_context)
            elif intent == 'schedule':
                response = self._generate_schedule_response(entities, query_type, user_context)
            elif intent == 'assignment':
                response = self._generate_assignment_response(entities, query_type, user_context)
            elif intent == 'exam':
                response = self._generate_exam_response(entities, query_type, user_context)
            elif intent == 'lecturer':
                response = self._generate_lecturer_response(entities, query_type)
            else:
                response = self._generate_unknown_response()
            
            return {
                'response': response,
                'intent': intent,
                'confidence': context['confidence'],
                'entities': entities,
                'query_type': query_type
            }
            
        except Exception as e:
            logging.error(f"Error generating response: {e}")
            return {
                'response': "I apologize, but I encountered an error processing your request. Please try again.",
                'intent': 'error',
                'confidence': 1.0,
                'entities': {},
                'query_type': 'error'
            }
    
    def _generate_greeting_response(self, user_context: Dict[str, Any] = None) -> str:
        """Generate greeting response"""
        if user_context and user_context.get('name'):
            return f"Hello {user_context['name']}! I'm your academic assistant. How can I help you today?"
        else:
            return "Hello! I'm your academic assistant. I can help you with course information, schedules, assignments, exams, and lecturer details. What would you like to know?"
    
    def _generate_goodbye_response(self) -> str:
        """Generate goodbye response"""
        responses = [
            "Goodbye! Feel free to ask me anything about your academics anytime.",
            "See you later! I'm here whenever you need academic assistance.",
            "Take care! Don't hesitate to reach out if you have any academic questions."
        ]
        import random
        return random.choice(responses)
    
    def _generate_course_info_response(self, entities: Dict[str, List[str]], 
                                     query_type: str, user_context: Dict[str, Any] = None) -> str:
        """Generate course information response"""
        courses = entities.get('courses', [])
        
        if courses:
            # Search for specific course
            course_code = courses[0].upper()
            course_data = DatabaseManager.search_courses(course_code)
            
            if course_data:
                course = course_data[0]
                if query_type == 'course_prerequisites':
                    prereqs = course.get('prerequisites', [])
                    if prereqs:
                        return f"The prerequisites for {course['code']} ({course['name']}) are: {', '.join(prereqs)}"
                    else:
                        return f"{course['code']} ({course['name']}) has no prerequisites."
                elif query_type == 'course_credits':
                    credits = course.get('credits', 'N/A')
                    return f"{course['code']} ({course['name']}) is worth {credits} credits."
                else:
                    return f"{course['code']}: {course['name']}\n\nDescription: {course.get('description', 'No description available.')}\nCredits: {course.get('credits', 'N/A')}\nProgram: {course.get('program', 'N/A')}\nYear: {course.get('year', 'N/A')}"
            else:
                return f"I couldn't find information about course {course_code}. Please check the course code and try again."
        else:
            # General course query
            if user_context and user_context.get('program') and user_context.get('year'):
                program = user_context['program']
                year = user_context['year']
                courses = DatabaseManager.get_academic_data('courses', {'program': program, 'year': year})
                
                if courses:
                    course_list = [f"• {course['code']}: {course['name']}" for course in courses[:5]]
                    response = f"Here are some courses for {program} Year {year}:\n\n" + "\n".join(course_list)
                    if len(courses) > 5:
                        response += f"\n\n... and {len(courses) - 5} more courses."
                    return response
                else:
                    return f"I couldn't find any courses for {program} Year {year}."
            else:
                return "Please specify a course code (e.g., CS101) or provide your program and year information for course listings."
    
    def _generate_schedule_response(self, entities: Dict[str, List[str]], 
                                  query_type: str, user_context: Dict[str, Any] = None) -> str:
        """Generate schedule response"""
        days = entities.get('days', [])
        courses = entities.get('courses', [])
        
        if query_type == 'schedule_by_day' and days:
            day = days[0].capitalize()
            schedules = DatabaseManager.get_academic_data('schedules', {'day': day})
            
            if schedules:
                schedule_list = []
                for schedule in schedules:
                    schedule_list.append(f"• {schedule['course_code']}: {schedule['start_time']}-{schedule['end_time']} in {schedule['room']}")
                
                return f"Schedule for {day}:\n\n" + "\n".join(schedule_list)
            else:
                return f"No classes scheduled for {day}."
        
        elif query_type == 'schedule_by_course' and courses:
            course_code = courses[0].upper()
            schedules = DatabaseManager.get_academic_data('schedules', {'course_code': course_code})
            
            if schedules:
                schedule_list = []
                for schedule in schedules:
                    schedule_list.append(f"• {schedule['day']}: {schedule['start_time']}-{schedule['end_time']} in {schedule['room']} ({schedule.get('type', 'Lecture')})")
                
                return f"Schedule for {course_code}:\n\n" + "\n".join(schedule_list)
            else:
                return f"No schedule found for course {course_code}."
        
        else:
            # General schedule query
            if user_context and user_context.get('program') and user_context.get('year'):
                # This would require joining schedules with courses by program/year
                return "To view your complete schedule, please specify a day (e.g., 'Monday schedule') or a specific course code."
            else:
                return "Please specify a day (e.g., 'Monday') or course code (e.g., 'CS101') to view the schedule."
    
    def _generate_assignment_response(self, entities: Dict[str, List[str]], 
                                    query_type: str, user_context: Dict[str, Any] = None) -> str:
        """Generate assignment response"""
        courses = entities.get('courses', [])
        
        if query_type == 'upcoming_assignments':
            assignments = DatabaseManager.get_academic_data('assignments')
            current_date = datetime.utcnow()
            
            upcoming = []
            for assignment in assignments:
                due_date = assignment.get('due_date')
                if isinstance(due_date, str):
                    try:
                        due_date = datetime.fromisoformat(due_date.replace('Z', '+00:00'))
                    except:
                        continue
                
                if due_date and due_date > current_date:
                    days_left = (due_date - current_date).days
                    upcoming.append({
                        'assignment': assignment,
                        'days_left': days_left
                    })
            
            # Sort by due date
            upcoming.sort(key=lambda x: x['days_left'])
            
            if upcoming:
                assignment_list = []
                for item in upcoming[:5]:  # Show top 5
                    assignment = item['assignment']
                    days_left = item['days_left']
                    assignment_list.append(f"• {assignment['course_code']}: {assignment['title']} (Due in {days_left} days)")
                
                return "Upcoming assignments:\n\n" + "\n".join(assignment_list)
            else:
                return "No upcoming assignments found."
        
        elif courses:
            course_code = courses[0].upper()
            assignments = DatabaseManager.get_academic_data('assignments', {'course_code': course_code})
            
            if assignments:
                assignment_list = []
                for assignment in assignments:
                    due_date = assignment.get('due_date', 'No due date')
                    if isinstance(due_date, datetime):
                        due_date = due_date.strftime('%Y-%m-%d')
                    assignment_list.append(f"• {assignment['title']} (Due: {due_date})")
                
                return f"Assignments for {course_code}:\n\n" + "\n".join(assignment_list)
            else:
                return f"No assignments found for course {course_code}."
        
        else:
            return "Please specify a course code to view assignments, or ask for 'upcoming assignments'."
    
    def _generate_exam_response(self, entities: Dict[str, List[str]], 
                              query_type: str, user_context: Dict[str, Any] = None) -> str:
        """Generate exam response"""
        courses = entities.get('courses', [])
        
        if query_type == 'upcoming_exams':
            exams = DatabaseManager.get_academic_data('exams')
            current_date = datetime.utcnow()
            
            upcoming = []
            for exam in exams:
                exam_date = exam.get('date')
                if isinstance(exam_date, str):
                    try:
                        exam_date = datetime.fromisoformat(exam_date.replace('Z', '+00:00'))
                    except:
                        continue
                
                if exam_date and exam_date > current_date:
                    days_left = (exam_date - current_date).days
                    upcoming.append({
                        'exam': exam,
                        'days_left': days_left
                    })
            
            # Sort by exam date
            upcoming.sort(key=lambda x: x['days_left'])
            
            if upcoming:
                exam_list = []
                for item in upcoming[:5]:  # Show top 5
                    exam = item['exam']
                    days_left = item['days_left']
                    exam_list.append(f"• {exam['course_code']}: {exam.get('type', 'Exam')} in {days_left} days at {exam.get('venue', 'TBA')}")
                
                return "Upcoming exams:\n\n" + "\n".join(exam_list)
            else:
                return "No upcoming exams found."
        
        elif courses:
            course_code = courses[0].upper()
            exams = DatabaseManager.get_academic_data('exams', {'course_code': course_code})
            
            if exams:
                exam_list = []
                for exam in exams:
                    exam_date = exam.get('date', 'No date set')
                    if isinstance(exam_date, datetime):
                        exam_date = exam_date.strftime('%Y-%m-%d')
                    exam_list.append(f"• {exam.get('type', 'Exam')}: {exam_date} at {exam.get('start_time', 'TBA')} in {exam.get('venue', 'TBA')}")
                
                return f"Exams for {course_code}:\n\n" + "\n".join(exam_list)
            else:
                return f"No exams found for course {course_code}."
        
        else:
            return "Please specify a course code to view exams, or ask for 'upcoming exams'."
    
    def _generate_lecturer_response(self, entities: Dict[str, List[str]], query_type: str) -> str:
        """Generate lecturer response"""
        people = entities.get('people', [])
        
        if people:
            lecturer_name = people[0]
            lecturer = DatabaseManager.get_lecturer_info(lecturer_name)
            
            if lecturer:
                if query_type == 'lecturer_office_hours':
                    office_hours = lecturer.get('office_hours', [])
                    if office_hours:
                        hours_list = [f"• {hour}" for hour in office_hours]
                        return f"Office hours for {lecturer['name']}:\n\n" + "\n".join(hours_list)
                    else:
                        return f"No office hours information available for {lecturer['name']}."
                
                elif query_type == 'lecturer_contact':
                    contact_info = []
                    if lecturer.get('email'):
                        contact_info.append(f"Email: {lecturer['email']}")
                    if lecturer.get('phone'):
                        contact_info.append(f"Phone: {lecturer['phone']}")
                    if lecturer.get('office'):
                        contact_info.append(f"Office: {lecturer['office']}")
                    
                    if contact_info:
                        return f"Contact information for {lecturer['name']}:\n\n" + "\n".join(contact_info)
                    else:
                        return f"No contact information available for {lecturer['name']}."
                
                else:
                    # General lecturer info
                    info = [f"Name: {lecturer['name']}"]
                    if lecturer.get('department'):
                        info.append(f"Department: {lecturer['department']}")
                    if lecturer.get('email'):
                        info.append(f"Email: {lecturer['email']}")
                    if lecturer.get('office'):
                        info.append(f"Office: {lecturer['office']}")
                    if lecturer.get('specializations'):
                        info.append(f"Specializations: {', '.join(lecturer['specializations'])}")
                    
                    return "\n".join(info)
            else:
                return f"I couldn't find information about lecturer '{lecturer_name}'. Please check the name and try again."
        
        else:
            return "Please specify a lecturer's name to get their information."
    
    def _generate_unknown_response(self) -> str:
        """Generate response for unknown intents"""
        responses = [
            "I'm not sure I understand. I can help you with course information, schedules, assignments, exams, and lecturer details. What would you like to know?",
            "Could you please rephrase your question? I can assist with academic queries like course details, class schedules, assignment deadlines, exam dates, and lecturer information.",
            "I didn't quite catch that. Try asking about courses, schedules, assignments, exams, or lecturers."
        ]
        import random
        return random.choice(responses)
