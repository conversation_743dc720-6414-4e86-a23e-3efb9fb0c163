import spacy
import re
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any

class NLPService:
    """Natural Language Processing service using spaCy"""
    
    def __init__(self):
        try:
            # Load spaCy model (you may need to download it first)
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            logging.warning("spaCy model 'en_core_web_sm' not found. Using blank model.")
            self.nlp = spacy.blank("en")
        
        # Academic-specific patterns
        self.course_patterns = [
            r'\b[A-Z]{2,4}\s*\d{3,4}\b',  # CS101, MATH 201, etc.
            r'\b[A-Z]{2,4}-\d{3,4}\b',    # CS-101, MATH-201, etc.
        ]
        
        self.time_patterns = [
            r'\b\d{1,2}:\d{2}\s*(AM|PM|am|pm)?\b',  # 2:30 PM, 14:30
            r'\b\d{1,2}\s*(AM|PM|am|pm)\b',         # 2 PM
        ]
        
        self.day_patterns = [
            r'\b(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b',
            r'\b(mon|tue|wed|thu|fri|sat|sun)\b',
            r'\b(today|tomorrow|yesterday)\b'
        ]
    
    def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """Extract entities from text using spaCy and custom patterns"""
        doc = self.nlp(text.lower())
        
        entities = {
            'courses': [],
            'times': [],
            'days': [],
            'people': [],
            'dates': [],
            'locations': []
        }
        
        # Extract using spaCy NER
        for ent in doc.ents:
            if ent.label_ == "PERSON":
                entities['people'].append(ent.text)
            elif ent.label_ == "DATE":
                entities['dates'].append(ent.text)
            elif ent.label_ in ["GPE", "LOC", "FAC"]:  # Locations
                entities['locations'].append(ent.text)
        
        # Extract course codes using patterns
        for pattern in self.course_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            entities['courses'].extend(matches)
        
        # Extract times using patterns
        for pattern in self.time_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            entities['times'].extend([match[0] if isinstance(match, tuple) else match for match in matches])
        
        # Extract days using patterns
        for pattern in self.day_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            entities['days'].extend(matches)
        
        # Remove duplicates
        for key in entities:
            entities[key] = list(set(entities[key]))
        
        return entities
    
    def classify_intent(self, text: str) -> Dict[str, Any]:
        """Basic intent classification based on keywords"""
        text_lower = text.lower()
        
        # Intent patterns
        intent_patterns = {
            'course_info': [
                'course', 'class', 'subject', 'what is', 'tell me about',
                'description', 'credits', 'prerequisites'
            ],
            'schedule': [
                'schedule', 'timetable', 'when', 'time', 'class time',
                'what time', 'when is', 'calendar'
            ],
            'assignment': [
                'assignment', 'homework', 'due', 'deadline', 'submit',
                'when is due', 'due date'
            ],
            'exam': [
                'exam', 'test', 'quiz', 'examination', 'midterm', 'final',
                'exam date', 'exam time'
            ],
            'lecturer': [
                'professor', 'lecturer', 'teacher', 'instructor', 'who teaches',
                'office hours', 'contact', 'email'
            ],
            'greeting': [
                'hello', 'hi', 'hey', 'good morning', 'good afternoon',
                'good evening', 'greetings'
            ],
            'goodbye': [
                'bye', 'goodbye', 'see you', 'farewell', 'thanks', 'thank you'
            ]
        }
        
        # Calculate scores for each intent
        intent_scores = {}
        for intent, keywords in intent_patterns.items():
            score = 0
            for keyword in keywords:
                if keyword in text_lower:
                    score += 1
            intent_scores[intent] = score / len(keywords) if keywords else 0
        
        # Get the intent with highest score
        best_intent = max(intent_scores, key=intent_scores.get)
        confidence = intent_scores[best_intent]
        
        # If confidence is too low, classify as unknown
        if confidence < 0.1:
            best_intent = 'unknown'
            confidence = 0.0
        
        return {
            'intent': best_intent,
            'confidence': confidence,
            'all_scores': intent_scores
        }
    
    def extract_academic_context(self, text: str, user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Extract academic-specific context from text"""
        entities = self.extract_entities(text)
        intent_info = self.classify_intent(text)
        
        context = {
            'entities': entities,
            'intent': intent_info['intent'],
            'confidence': intent_info['confidence'],
            'user_program': user_context.get('program') if user_context else None,
            'user_year': user_context.get('year') if user_context else None,
            'query_type': self._determine_query_type(text, entities, intent_info['intent'])
        }
        
        return context
    
    def _determine_query_type(self, text: str, entities: Dict[str, List[str]], intent: str) -> str:
        """Determine the specific type of academic query"""
        text_lower = text.lower()
        
        if intent == 'course_info':
            if 'prerequisite' in text_lower:
                return 'course_prerequisites'
            elif 'credit' in text_lower:
                return 'course_credits'
            else:
                return 'course_general'
        
        elif intent == 'schedule':
            if entities['days']:
                return 'schedule_by_day'
            elif entities['courses']:
                return 'schedule_by_course'
            else:
                return 'schedule_general'
        
        elif intent == 'assignment':
            if 'upcoming' in text_lower or 'next' in text_lower:
                return 'upcoming_assignments'
            elif entities['courses']:
                return 'course_assignments'
            else:
                return 'assignment_general'
        
        elif intent == 'exam':
            if 'upcoming' in text_lower or 'next' in text_lower:
                return 'upcoming_exams'
            elif entities['courses']:
                return 'course_exams'
            else:
                return 'exam_general'
        
        elif intent == 'lecturer':
            if 'office hours' in text_lower:
                return 'lecturer_office_hours'
            elif 'contact' in text_lower or 'email' in text_lower:
                return 'lecturer_contact'
            else:
                return 'lecturer_general'
        
        return intent
