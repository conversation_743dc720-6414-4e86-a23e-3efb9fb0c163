import requests
import logging
from typing import Dict, Any, Optional
from flask import current_app
from database import DatabaseManager
from services.response_generator import ResponseGenerator

class RasaService:
    """Service for interacting with Rasa chatbot"""
    
    def __init__(self):
        self.rasa_url = None
        self.response_generator = ResponseGenerator()
    
    def _get_rasa_url(self):
        """Get Rasa server URL from config"""
        if not self.rasa_url:
            try:
                self.rasa_url = current_app.config.get('RASA_SERVER_URL', 'http://localhost:5005')
            except RuntimeError:
                # If not in Flask context, use default
                self.rasa_url = 'http://localhost:5005'
        return self.rasa_url
    
    def get_response(self, message: str, sender_id: str, user_context: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """Get response from Rasa or fallback to rule-based system"""
        try:
            # Try to get response from <PERSON>sa first
            rasa_response = self._query_rasa(message, sender_id)
            
            if rasa_response and rasa_response.get('text'):
                return rasa_response
            else:
                # Fallback to rule-based response generation
                return self._generate_fallback_response(message, user_context)
                
        except Exception as e:
            logging.error(f"Rasa service error: {e}")
            # Fallback to rule-based response generation
            return self._generate_fallback_response(message, user_context)
    
    def _query_rasa(self, message: str, sender_id: str) -> Optional[Dict[str, Any]]:
        """Query Rasa server for response"""
        try:
            rasa_url = self._get_rasa_url()
            endpoint = f"{rasa_url}/webhooks/rest/webhook"
            
            payload = {
                "sender": sender_id,
                "message": message
            }
            
            response = requests.post(endpoint, json=payload, timeout=5)
            
            if response.status_code == 200:
                rasa_responses = response.json()
                if rasa_responses and len(rasa_responses) > 0:
                    # Return the first response
                    return rasa_responses[0]
            
            return None
            
        except requests.exceptions.RequestException as e:
            logging.warning(f"Rasa server not available: {e}")
            return None
        except Exception as e:
            logging.error(f"Error querying Rasa: {e}")
            return None
    
    def _generate_fallback_response(self, message: str, user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate response using rule-based system when Rasa is not available"""
        try:
            response_data = self.response_generator.generate_response(message, user_context)
            
            return {
                'text': response_data['response'],
                'intent': {
                    'name': response_data.get('intent'),
                    'confidence': response_data.get('confidence', 0.8)
                },
                'entities': response_data.get('entities', []),
                'source': 'fallback'
            }
            
        except Exception as e:
            logging.error(f"Error generating fallback response: {e}")
            return {
                'text': "I apologize, but I'm having trouble processing your request right now. Please try again later.",
                'intent': {'name': 'error', 'confidence': 1.0},
                'entities': [],
                'source': 'error'
            }
    
    def train_model(self, training_data: Dict[str, Any]) -> bool:
        """Train Rasa model with new data"""
        try:
            rasa_url = self._get_rasa_url()
            endpoint = f"{rasa_url}/model/train"
            
            response = requests.post(endpoint, json=training_data, timeout=30)
            
            if response.status_code == 200:
                logging.info("Rasa model training initiated successfully")
                return True
            else:
                logging.error(f"Failed to train Rasa model: {response.status_code}")
                return False
                
        except Exception as e:
            logging.error(f"Error training Rasa model: {e}")
            return False
    
    def get_model_status(self) -> Dict[str, Any]:
        """Get current Rasa model status"""
        try:
            rasa_url = self._get_rasa_url()
            endpoint = f"{rasa_url}/status"
            
            response = requests.get(endpoint, timeout=5)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {'status': 'unavailable', 'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            logging.error(f"Error getting Rasa status: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def parse_message(self, message: str) -> Dict[str, Any]:
        """Parse message to extract intent and entities"""
        try:
            rasa_url = self._get_rasa_url()
            endpoint = f"{rasa_url}/model/parse"
            
            payload = {"text": message}
            response = requests.post(endpoint, json=payload, timeout=5)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {'intent': {'name': 'unknown', 'confidence': 0.0}, 'entities': []}
                
        except Exception as e:
            logging.error(f"Error parsing message with Rasa: {e}")
            return {'intent': {'name': 'unknown', 'confidence': 0.0}, 'entities': []}
    
    def add_conversation_to_tracker(self, sender_id: str, events: list) -> bool:
        """Add conversation events to Rasa tracker"""
        try:
            rasa_url = self._get_rasa_url()
            endpoint = f"{rasa_url}/conversations/{sender_id}/tracker/events"
            
            response = requests.post(endpoint, json=events, timeout=5)
            
            return response.status_code == 200
            
        except Exception as e:
            logging.error(f"Error adding events to tracker: {e}")
            return False
