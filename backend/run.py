#!/usr/bin/env python3
"""
Development server runner for the Academic Chatbot API
"""

import os
from app import create_app

if __name__ == '__main__':
    # Create the Flask app
    app = create_app()
    
    # Get configuration from environment
    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_ENV') == 'development'
    
    print(f"Starting Academic Chatbot API server...")
    print(f"Environment: {os.environ.get('FLASK_ENV', 'development')}")
    print(f"Server: http://{host}:{port}")
    print(f"Debug mode: {debug}")
    
    # Run the application
    app.run(
        host=host,
        port=port,
        debug=debug,
        threaded=True
    )
