#!/usr/bin/env python3
"""
Database seeding script for the Academic Chatbot
Run this script to populate the database with sample data
"""

import os
import sys
from pymongo import MongoClient
from datetime import datetime
import logging

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.sample_data import (
    SAMPLE_COURSES,
    SAMPLE_LECTURERS,
    SAMPLE_SCHEDULES,
    SAMPLE_ASSIGNMENTS,
    SAMPLE_EXAMS
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_database_connection():
    """Get MongoDB database connection"""
    try:
        # Get MongoDB URI from environment or use default
        mongo_uri = os.environ.get('MONGO_URI', 'mongodb://localhost:27017/academic_chatbot')
        client = MongoClient(mongo_uri)
        
        # Extract database name from URI
        db_name = mongo_uri.split('/')[-1]
        db = client[db_name]
        
        # Test connection
        db.command('ping')
        logger.info(f"Connected to MongoDB database: {db_name}")
        return db
    
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB: {e}")
        return None

def seed_collection(db, collection_name, data, clear_existing=True):
    """Seed a collection with sample data"""
    try:
        collection = db[collection_name]
        
        if clear_existing:
            # Clear existing data
            result = collection.delete_many({})
            logger.info(f"Cleared {result.deleted_count} existing documents from {collection_name}")
        
        # Insert new data
        if data:
            result = collection.insert_many(data)
            logger.info(f"Inserted {len(result.inserted_ids)} documents into {collection_name}")
        else:
            logger.warning(f"No data provided for {collection_name}")
    
    except Exception as e:
        logger.error(f"Error seeding {collection_name}: {e}")

def create_indexes(db):
    """Create database indexes for better performance"""
    try:
        # Courses indexes
        db.courses.create_index("code")
        db.courses.create_index("program")
        db.courses.create_index([("program", 1), ("year", 1)])
        
        # Lecturers indexes
        db.lecturers.create_index("name")
        db.lecturers.create_index("department")
        
        # Schedules indexes
        db.schedules.create_index("course_code")
        db.schedules.create_index("day")
        
        # Assignments indexes
        db.assignments.create_index("course_code")
        db.assignments.create_index("due_date")
        
        # Exams indexes
        db.exams.create_index("course_code")
        db.exams.create_index("date")
        
        # Users indexes
        db.users.create_index("user_id", unique=True)
        
        # Conversations indexes
        db.conversations.create_index("user_id")
        db.conversations.create_index("timestamp")
        
        logger.info("Created database indexes")
    
    except Exception as e:
        logger.error(f"Error creating indexes: {e}")

def main():
    """Main seeding function"""
    logger.info("Starting database seeding...")
    
    # Get database connection
    db = get_database_connection()
    if not db:
        logger.error("Cannot proceed without database connection")
        return False
    
    try:
        # Seed collections
        seed_collection(db, 'courses', SAMPLE_COURSES)
        seed_collection(db, 'lecturers', SAMPLE_LECTURERS)
        seed_collection(db, 'schedules', SAMPLE_SCHEDULES)
        seed_collection(db, 'assignments', SAMPLE_ASSIGNMENTS)
        seed_collection(db, 'exams', SAMPLE_EXAMS)
        
        # Create indexes
        create_indexes(db)
        
        logger.info("Database seeding completed successfully!")
        
        # Print summary
        print("\n" + "="*50)
        print("DATABASE SEEDING SUMMARY")
        print("="*50)
        print(f"Courses: {len(SAMPLE_COURSES)} documents")
        print(f"Lecturers: {len(SAMPLE_LECTURERS)} documents")
        print(f"Schedules: {len(SAMPLE_SCHEDULES)} documents")
        print(f"Assignments: {len(SAMPLE_ASSIGNMENTS)} documents")
        print(f"Exams: {len(SAMPLE_EXAMS)} documents")
        print("="*50)
        
        return True
    
    except Exception as e:
        logger.error(f"Error during seeding: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
