from datetime import datetime, <PERSON>elta

# Sample academic data for seeding the database

SAMPLE_COURSES = [
    {
        "code": "CS101",
        "name": "Introduction to Computer Science",
        "description": "Fundamental concepts of computer science including programming basics, algorithms, and data structures.",
        "credits": 3,
        "program": "Computer Science",
        "year": 1,
        "semester": "Fall",
        "prerequisites": [],
        "lecturer": "<PERSON><PERSON>"
    },
    {
        "code": "CS201",
        "name": "Data Structures and Algorithms",
        "description": "Advanced data structures, algorithm design and analysis, complexity theory.",
        "credits": 4,
        "program": "Computer Science",
        "year": 2,
        "semester": "Spring",
        "prerequisites": ["CS101"],
        "lecturer": "<PERSON><PERSON>"
    },
    {
        "code": "MATH101",
        "name": "Calculus I",
        "description": "Limits, derivatives, and applications of differential calculus.",
        "credits": 4,
        "program": "Mathematics",
        "year": 1,
        "semester": "Fall",
        "prerequisites": [],
        "lecturer": "<PERSON><PERSON>"
    },
    {
        "code": "MATH201",
        "name": "Linear Algebra",
        "description": "Vector spaces, matrices, linear transformations, and eigenvalues.",
        "credits": 3,
        "program": "Mathematics",
        "year": 2,
        "semester": "Spring",
        "prerequisites": ["MATH101"],
        "lecturer": "Prof. <PERSON>"
    },
    {
        "code": "ENG101",
        "name": "English Composition",
        "description": "Academic writing, research methods, and critical thinking skills.",
        "credits": 3,
        "program": "English",
        "year": 1,
        "semester": "Fall",
        "prerequisites": [],
        "lecturer": "Dr. Wilson"
    },
    {
        "code": "PHYS101",
        "name": "General Physics I",
        "description": "Mechanics, thermodynamics, and wave motion.",
        "credits": 4,
        "program": "Physics",
        "year": 1,
        "semester": "Fall",
        "prerequisites": ["MATH101"],
        "lecturer": "Prof. Taylor"
    }
]

SAMPLE_LECTURERS = [
    {
        "name": "Dr. Smith",
        "email": "<EMAIL>",
        "department": "Computer Science",
        "office": "CS Building, Room 201",
        "phone": "******-0101",
        "office_hours": ["Monday 2:00-4:00 PM", "Wednesday 10:00-12:00 PM"],
        "courses": ["CS101"],
        "specializations": ["Programming", "Software Engineering", "Algorithms"]
    },
    {
        "name": "Prof. Johnson",
        "email": "<EMAIL>",
        "department": "Computer Science",
        "office": "CS Building, Room 205",
        "phone": "******-0102",
        "office_hours": ["Tuesday 1:00-3:00 PM", "Thursday 9:00-11:00 AM"],
        "courses": ["CS201"],
        "specializations": ["Data Structures", "Algorithm Analysis", "Computational Complexity"]
    },
    {
        "name": "Dr. Brown",
        "email": "<EMAIL>",
        "department": "Mathematics",
        "office": "Math Building, Room 301",
        "phone": "******-0103",
        "office_hours": ["Monday 10:00-12:00 PM", "Friday 2:00-4:00 PM"],
        "courses": ["MATH101"],
        "specializations": ["Calculus", "Mathematical Analysis", "Real Analysis"]
    },
    {
        "name": "Prof. Davis",
        "email": "<EMAIL>",
        "department": "Mathematics",
        "office": "Math Building, Room 305",
        "phone": "******-0104",
        "office_hours": ["Wednesday 11:00 AM-1:00 PM", "Thursday 3:00-5:00 PM"],
        "courses": ["MATH201"],
        "specializations": ["Linear Algebra", "Abstract Algebra", "Matrix Theory"]
    },
    {
        "name": "Dr. Wilson",
        "email": "<EMAIL>",
        "department": "English",
        "office": "Humanities Building, Room 101",
        "phone": "******-0105",
        "office_hours": ["Tuesday 10:00 AM-12:00 PM", "Thursday 1:00-3:00 PM"],
        "courses": ["ENG101"],
        "specializations": ["Academic Writing", "Rhetoric", "Composition"]
    },
    {
        "name": "Prof. Taylor",
        "email": "<EMAIL>",
        "department": "Physics",
        "office": "Physics Building, Room 201",
        "phone": "******-0106",
        "office_hours": ["Monday 9:00-11:00 AM", "Wednesday 2:00-4:00 PM"],
        "courses": ["PHYS101"],
        "specializations": ["Classical Mechanics", "Thermodynamics", "Wave Physics"]
    }
]

SAMPLE_SCHEDULES = [
    {
        "course_code": "CS101",
        "day": "Monday",
        "start_time": "09:00",
        "end_time": "10:30",
        "room": "CS Building, Room 101",
        "type": "Lecture",
        "lecturer": "Dr. Smith"
    },
    {
        "course_code": "CS101",
        "day": "Wednesday",
        "start_time": "09:00",
        "end_time": "10:30",
        "room": "CS Building, Room 101",
        "type": "Lecture",
        "lecturer": "Dr. Smith"
    },
    {
        "course_code": "CS101",
        "day": "Friday",
        "start_time": "14:00",
        "end_time": "15:30",
        "room": "CS Lab 1",
        "type": "Lab",
        "lecturer": "TA Johnson"
    },
    {
        "course_code": "CS201",
        "day": "Tuesday",
        "start_time": "11:00",
        "end_time": "12:30",
        "room": "CS Building, Room 102",
        "type": "Lecture",
        "lecturer": "Prof. Johnson"
    },
    {
        "course_code": "CS201",
        "day": "Thursday",
        "start_time": "11:00",
        "end_time": "12:30",
        "room": "CS Building, Room 102",
        "type": "Lecture",
        "lecturer": "Prof. Johnson"
    },
    {
        "course_code": "MATH101",
        "day": "Monday",
        "start_time": "11:00",
        "end_time": "12:00",
        "room": "Math Building, Room 201",
        "type": "Lecture",
        "lecturer": "Dr. Brown"
    },
    {
        "course_code": "MATH101",
        "day": "Wednesday",
        "start_time": "11:00",
        "end_time": "12:00",
        "room": "Math Building, Room 201",
        "type": "Lecture",
        "lecturer": "Dr. Brown"
    },
    {
        "course_code": "MATH101",
        "day": "Friday",
        "start_time": "11:00",
        "end_time": "12:00",
        "room": "Math Building, Room 201",
        "type": "Lecture",
        "lecturer": "Dr. Brown"
    },
    {
        "course_code": "MATH201",
        "day": "Tuesday",
        "start_time": "14:00",
        "end_time": "15:30",
        "room": "Math Building, Room 202",
        "type": "Lecture",
        "lecturer": "Prof. Davis"
    },
    {
        "course_code": "MATH201",
        "day": "Thursday",
        "start_time": "14:00",
        "end_time": "15:30",
        "room": "Math Building, Room 202",
        "type": "Lecture",
        "lecturer": "Prof. Davis"
    }
]

# Generate sample assignments with future due dates
def generate_sample_assignments():
    base_date = datetime.utcnow()
    assignments = [
        {
            "title": "Programming Assignment 1: Hello World",
            "course_code": "CS101",
            "due_date": base_date + timedelta(days=7),
            "description": "Write a simple Hello World program in Python with proper documentation.",
            "type": "assignment",
            "weight": 10,
            "status": "active"
        },
        {
            "title": "Data Structure Implementation",
            "course_code": "CS201",
            "due_date": base_date + timedelta(days=14),
            "description": "Implement a binary search tree with insert, delete, and search operations.",
            "type": "project",
            "weight": 25,
            "status": "active"
        },
        {
            "title": "Calculus Problem Set 3",
            "course_code": "MATH101",
            "due_date": base_date + timedelta(days=5),
            "description": "Solve problems related to derivatives and applications.",
            "type": "homework",
            "weight": 15,
            "status": "active"
        },
        {
            "title": "Linear Algebra Quiz 2",
            "course_code": "MATH201",
            "due_date": base_date + timedelta(days=3),
            "description": "Online quiz covering matrix operations and vector spaces.",
            "type": "quiz",
            "weight": 10,
            "status": "active"
        },
        {
            "title": "Essay: Academic Writing Techniques",
            "course_code": "ENG101",
            "due_date": base_date + timedelta(days=10),
            "description": "Write a 1500-word essay on effective academic writing techniques.",
            "type": "essay",
            "weight": 20,
            "status": "active"
        }
    ]
    return assignments

# Generate sample exams with future dates
def generate_sample_exams():
    base_date = datetime.utcnow()
    exams = [
        {
            "course_code": "CS101",
            "date": base_date + timedelta(days=30),
            "start_time": "09:00",
            "duration": 120,
            "venue": "Main Hall A",
            "type": "midterm",
            "instructions": "Bring student ID and calculator. No electronic devices allowed."
        },
        {
            "course_code": "CS201",
            "date": base_date + timedelta(days=45),
            "start_time": "14:00",
            "duration": 180,
            "venue": "CS Building, Room 201",
            "type": "final",
            "instructions": "Comprehensive exam covering all course material."
        },
        {
            "course_code": "MATH101",
            "date": base_date + timedelta(days=25),
            "start_time": "10:00",
            "duration": 90,
            "venue": "Math Building, Room 301",
            "type": "midterm",
            "instructions": "Calculator allowed. Show all work for partial credit."
        },
        {
            "course_code": "MATH201",
            "date": base_date + timedelta(days=40),
            "start_time": "11:00",
            "duration": 150,
            "venue": "Main Hall B",
            "type": "final",
            "instructions": "Closed book exam. Formula sheet will be provided."
        },
        {
            "course_code": "ENG101",
            "date": base_date + timedelta(days=35),
            "start_time": "13:00",
            "duration": 120,
            "venue": "Humanities Building, Room 201",
            "type": "final",
            "instructions": "In-class essay exam. Dictionary allowed."
        }
    ]
    return exams

SAMPLE_ASSIGNMENTS = generate_sample_assignments()
SAMPLE_EXAMS = generate_sample_exams()
