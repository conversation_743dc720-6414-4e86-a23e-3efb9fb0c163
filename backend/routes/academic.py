from flask import Blueprint, request, jsonify
from database import DatabaseManager
import logging
from datetime import datetime, timedelta

academic_bp = Blueprint('academic', __name__)

@academic_bp.route('/courses', methods=['GET'])
def get_courses():
    """Get courses based on filters"""
    try:
        program = request.args.get('program')
        year = request.args.get('year', type=int)
        semester = request.args.get('semester')
        search = request.args.get('search')
        
        if search:
            # Search courses by name or code
            courses = DatabaseManager.search_courses(search, program)
        else:
            # Filter courses
            filters = {}
            if program:
                filters['program'] = program
            if year:
                filters['year'] = year
            if semester:
                filters['semester'] = semester
            
            courses = DatabaseManager.get_academic_data('courses', filters)
        
        # Clean up MongoDB ObjectIds
        for course in courses:
            course.pop('_id', None)
        
        return jsonify({
            'success': True,
            'courses': courses
        }), 200
        
    except Exception as e:
        logging.error(f"Get courses error: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500

@academic_bp.route('/schedules', methods=['GET'])
def get_schedules():
    """Get class schedules"""
    try:
        program = request.args.get('program')
        year = request.args.get('year', type=int)
        day = request.args.get('day')
        
        filters = {}
        if day:
            filters['day'] = day
        
        schedules = DatabaseManager.get_academic_data('schedules', filters)
        
        # Filter by program and year if provided
        if program or year:
            filtered_schedules = []
            for schedule in schedules:
                # Get course info to check program/year
                course_filters = {'code': schedule.get('course_code')}
                if program:
                    course_filters['program'] = program
                if year:
                    course_filters['year'] = year
                
                courses = DatabaseManager.get_academic_data('courses', course_filters)
                if courses:
                    filtered_schedules.append(schedule)
            schedules = filtered_schedules
        
        # Clean up MongoDB ObjectIds
        for schedule in schedules:
            schedule.pop('_id', None)
        
        return jsonify({
            'success': True,
            'schedules': schedules
        }), 200
        
    except Exception as e:
        logging.error(f"Get schedules error: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500

@academic_bp.route('/assignments', methods=['GET'])
def get_assignments():
    """Get assignments and deadlines"""
    try:
        course_code = request.args.get('course_code')
        upcoming = request.args.get('upcoming', 'false').lower() == 'true'
        
        filters = {}
        if course_code:
            filters['course_code'] = course_code
        
        assignments = DatabaseManager.get_academic_data('assignments', filters)
        
        # Filter upcoming assignments
        if upcoming:
            current_date = datetime.utcnow()
            upcoming_assignments = []
            for assignment in assignments:
                due_date = assignment.get('due_date')
                if isinstance(due_date, str):
                    due_date = datetime.fromisoformat(due_date.replace('Z', '+00:00'))
                if due_date and due_date > current_date:
                    upcoming_assignments.append(assignment)
            assignments = upcoming_assignments
        
        # Clean up MongoDB ObjectIds
        for assignment in assignments:
            assignment.pop('_id', None)
        
        return jsonify({
            'success': True,
            'assignments': assignments
        }), 200
        
    except Exception as e:
        logging.error(f"Get assignments error: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500

@academic_bp.route('/exams', methods=['GET'])
def get_exams():
    """Get exam schedules"""
    try:
        course_code = request.args.get('course_code')
        upcoming = request.args.get('upcoming', 'false').lower() == 'true'
        
        filters = {}
        if course_code:
            filters['course_code'] = course_code
        
        exams = DatabaseManager.get_academic_data('exams', filters)
        
        # Filter upcoming exams
        if upcoming:
            current_date = datetime.utcnow()
            upcoming_exams = []
            for exam in exams:
                exam_date = exam.get('date')
                if isinstance(exam_date, str):
                    exam_date = datetime.fromisoformat(exam_date.replace('Z', '+00:00'))
                if exam_date and exam_date > current_date:
                    upcoming_exams.append(exam)
            exams = upcoming_exams
        
        # Clean up MongoDB ObjectIds
        for exam in exams:
            exam.pop('_id', None)
        
        return jsonify({
            'success': True,
            'exams': exams
        }), 200
        
    except Exception as e:
        logging.error(f"Get exams error: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500

@academic_bp.route('/lecturers', methods=['GET'])
def get_lecturers():
    """Get lecturer information"""
    try:
        name = request.args.get('name')
        department = request.args.get('department')
        
        if name:
            lecturer = DatabaseManager.get_lecturer_info(name)
            if lecturer:
                lecturer.pop('_id', None)
                return jsonify({
                    'success': True,
                    'lecturer': lecturer
                }), 200
            else:
                return jsonify({
                    'success': False,
                    'message': 'Lecturer not found'
                }), 404
        else:
            filters = {}
            if department:
                filters['department'] = department
            
            lecturers = DatabaseManager.get_academic_data('lecturers', filters)
            
            # Clean up MongoDB ObjectIds
            for lecturer in lecturers:
                lecturer.pop('_id', None)
            
            return jsonify({
                'success': True,
                'lecturers': lecturers
            }), 200
        
    except Exception as e:
        logging.error(f"Get lecturers error: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500

@academic_bp.route('/programs', methods=['GET'])
def get_programs():
    """Get available academic programs"""
    try:
        # Get unique programs from courses collection
        programs = DatabaseManager.mongo.db.courses.distinct('program')
        
        return jsonify({
            'success': True,
            'programs': programs
        }), 200
        
    except Exception as e:
        logging.error(f"Get programs error: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500
