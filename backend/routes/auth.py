from flask import Blueprint, request, jsonify
from database import DatabaseManager
from models.user import User
import uuid
import logging

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['POST'])
def login():
    """Handle user login/registration"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['name', 'program', 'year']
        if not all(field in data for field in required_fields):
            return jsonify({
                'success': False,
                'message': 'Missing required fields: name, program, year'
            }), 400
        
        # Generate user ID if not provided
        user_id = data.get('user_id', str(uuid.uuid4()))
        
        # Create user object
        user = User(
            user_id=user_id,
            name=data['name'],
            program=data['program'],
            year=int(data['year']),
            email=data.get('email')
        )
        
        # Save to database
        success = DatabaseManager.create_or_update_user(user.to_dict())
        
        if success:
            return jsonify({
                'success': True,
                'message': 'User profile saved successfully',
                'user': {
                    'user_id': user.user_id,
                    'name': user.name,
                    'program': user.program,
                    'year': user.year,
                    'email': user.email
                }
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to save user profile'
            }), 500
            
    except Exception as e:
        logging.error(f"Login error: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500

@auth_bp.route('/profile/<user_id>', methods=['GET'])
def get_profile(user_id):
    """Get user profile by ID"""
    try:
        user_data = DatabaseManager.get_user_profile(user_id)
        
        if user_data:
            # Remove MongoDB ObjectId for JSON serialization
            user_data.pop('_id', None)
            return jsonify({
                'success': True,
                'user': user_data
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': 'User not found'
            }), 404
            
    except Exception as e:
        logging.error(f"Get profile error: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500

@auth_bp.route('/profile/<user_id>', methods=['PUT'])
def update_profile(user_id):
    """Update user profile"""
    try:
        data = request.get_json()
        
        # Get existing user
        existing_user = DatabaseManager.get_user_profile(user_id)
        if not existing_user:
            return jsonify({
                'success': False,
                'message': 'User not found'
            }), 404
        
        # Update user data
        user_data = existing_user.copy()
        user_data.update({
            'name': data.get('name', user_data['name']),
            'program': data.get('program', user_data['program']),
            'year': int(data.get('year', user_data['year'])),
            'email': data.get('email', user_data.get('email'))
        })
        
        # Save updated profile
        success = DatabaseManager.create_or_update_user(user_data)
        
        if success:
            user_data.pop('_id', None)
            return jsonify({
                'success': True,
                'message': 'Profile updated successfully',
                'user': user_data
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to update profile'
            }), 500
            
    except Exception as e:
        logging.error(f"Update profile error: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500

@auth_bp.route('/conversation-history/<user_id>', methods=['GET'])
def get_conversation_history(user_id):
    """Get conversation history for a user"""
    try:
        limit = request.args.get('limit', 20, type=int)
        conversations = DatabaseManager.get_conversation_history(user_id, limit)
        
        # Clean up MongoDB ObjectIds
        for conv in conversations:
            conv.pop('_id', None)
        
        return jsonify({
            'success': True,
            'conversations': conversations
        }), 200
        
    except Exception as e:
        logging.error(f"Get conversation history error: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500
