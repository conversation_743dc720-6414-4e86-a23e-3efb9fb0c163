from flask import Blueprint, request, jsonify
from database import DatabaseManager
from services.nlp_service import NLPService
from services.rasa_service import RasaService
import logging
from datetime import datetime

chat_bp = Blueprint('chat', __name__)

@chat_bp.route('/message', methods=['POST'])
def handle_message():
    """Handle incoming chat messages"""
    try:
        data = request.get_json()
        
        # Validate required fields
        if not data.get('message') or not data.get('user_id'):
            return jsonify({
                'success': False,
                'message': 'Missing required fields: message, user_id'
            }), 400
        
        user_id = data['user_id']
        user_message = data['message'].strip()
        
        # Get user profile for context
        user_profile = DatabaseManager.get_user_profile(user_id)
        if not user_profile:
            return jsonify({
                'success': False,
                'message': 'User not found'
            }), 404
        
        # Process message through NLP pipeline
        nlp_service = NLPService()
        rasa_service = RasaService()
        
        # Extract entities and intent
        entities = nlp_service.extract_entities(user_message)
        
        # Get response from Rasa
        rasa_response = rasa_service.get_response(
            message=user_message,
            sender_id=user_id,
            user_context={
                'program': user_profile.get('program'),
                'year': user_profile.get('year'),
                'name': user_profile.get('name')
            }
        )
        
        if rasa_response:
            bot_response = rasa_response.get('text', 'I apologize, but I encountered an issue processing your request.')
            intent = rasa_response.get('intent', {}).get('name')
            confidence = rasa_response.get('intent', {}).get('confidence')
        else:
            # Fallback response
            bot_response = "I'm sorry, I'm having trouble understanding your request right now. Could you please try rephrasing it?"
            intent = None
            confidence = None
        
        # Save conversation to database
        conversation_id = DatabaseManager.save_conversation(
            user_id=user_id,
            message=user_message,
            response=bot_response,
            intent=intent,
            confidence=confidence
        )
        
        # Increment user conversation count
        user_profile['conversation_count'] = user_profile.get('conversation_count', 0) + 1
        DatabaseManager.create_or_update_user(user_profile)
        
        return jsonify({
            'success': True,
            'response': bot_response,
            'intent': intent,
            'confidence': confidence,
            'entities': entities,
            'conversation_id': conversation_id,
            'timestamp': datetime.utcnow().isoformat()
        }), 200
        
    except Exception as e:
        logging.error(f"Chat message error: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error',
            'response': 'I apologize, but I encountered an error. Please try again.'
        }), 500

@chat_bp.route('/feedback', methods=['POST'])
def handle_feedback():
    """Handle user feedback on bot responses"""
    try:
        data = request.get_json()
        
        required_fields = ['conversation_id', 'feedback']
        if not all(field in data for field in required_fields):
            return jsonify({
                'success': False,
                'message': 'Missing required fields: conversation_id, feedback'
            }), 400
        
        conversation_id = data['conversation_id']
        feedback = data['feedback']  # 'positive', 'negative', or rating
        
        # Update conversation with feedback
        # This would require updating the database schema to support feedback
        # For now, we'll log it
        logging.info(f"Feedback received for conversation {conversation_id}: {feedback}")
        
        return jsonify({
            'success': True,
            'message': 'Feedback recorded successfully'
        }), 200
        
    except Exception as e:
        logging.error(f"Feedback error: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500

@chat_bp.route('/context/<user_id>', methods=['GET'])
def get_conversation_context(user_id):
    """Get recent conversation context for better responses"""
    try:
        limit = request.args.get('limit', 5, type=int)
        conversations = DatabaseManager.get_conversation_history(user_id, limit)
        
        # Format context for NLP processing
        context = []
        for conv in conversations:
            context.append({
                'user_message': conv.get('user_message'),
                'bot_response': conv.get('bot_response'),
                'intent': conv.get('intent'),
                'timestamp': conv.get('timestamp')
            })
        
        return jsonify({
            'success': True,
            'context': context
        }), 200
        
    except Exception as e:
        logging.error(f"Get context error: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500

@chat_bp.route('/quick-responses', methods=['GET'])
def get_quick_responses():
    """Get suggested quick responses based on common queries"""
    try:
        quick_responses = [
            "What are my course schedules?",
            "When is my next assignment due?",
            "Show me exam dates",
            "Who is teaching CS101?",
            "What are the office hours for Dr. Smith?",
            "Tell me about course prerequisites",
            "How many credits do I need to graduate?"
        ]
        
        return jsonify({
            'success': True,
            'quick_responses': quick_responses
        }), 200
        
    except Exception as e:
        logging.error(f"Quick responses error: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500
