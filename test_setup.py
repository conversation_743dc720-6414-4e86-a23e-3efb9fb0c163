#!/usr/bin/env python3
"""
Test script to verify the AI Academic Chatbot setup
"""

import sys
import os
import importlib
import subprocess
from pathlib import Path

def test_python_version():
    """Test Python version"""
    print("🐍 Testing Python version...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Need Python 3.8+")
        return False

def test_required_packages():
    """Test required Python packages"""
    print("\n📦 Testing required packages...")
    required_packages = [
        'flask',
        'flask_cors',
        'flask_pymongo',
        'pymongo',
        'spacy',
        'requests'
    ]
    
    all_good = True
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {package} - OK")
        except ImportError:
            print(f"❌ {package} - Missing")
            all_good = False
    
    return all_good

def test_spacy_model():
    """Test spaCy English model"""
    print("\n🧠 Testing spaCy model...")
    try:
        import spacy
        nlp = spacy.load("en_core_web_sm")
        print("✅ spaCy en_core_web_sm model - OK")
        return True
    except OSError:
        print("❌ spaCy en_core_web_sm model - Missing")
        print("   Run: python -m spacy download en_core_web_sm")
        return False

def test_mongodb_connection():
    """Test MongoDB connection"""
    print("\n🍃 Testing MongoDB connection...")
    try:
        from pymongo import MongoClient
        client = MongoClient('mongodb://localhost:27017/', serverSelectionTimeoutMS=2000)
        client.server_info()
        print("✅ MongoDB connection - OK")
        return True
    except Exception as e:
        print(f"❌ MongoDB connection - Failed: {e}")
        print("   Make sure MongoDB is running")
        return False

def test_file_structure():
    """Test project file structure"""
    print("\n📁 Testing file structure...")
    required_files = [
        'backend/app.py',
        'backend/config.py',
        'backend/database.py',
        'backend/requirements.txt',
        'frontend/package.json',
        'frontend/src/App.jsx',
        'chatbot/config.yml',
        'chatbot/domain.yml'
    ]
    
    all_good = True
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path} - OK")
        else:
            print(f"❌ {file_path} - Missing")
            all_good = False
    
    return all_good

def test_node_and_npm():
    """Test Node.js and npm"""
    print("\n🟢 Testing Node.js and npm...")
    try:
        # Test Node.js
        node_result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if node_result.returncode == 0:
            print(f"✅ Node.js {node_result.stdout.strip()} - OK")
        else:
            print("❌ Node.js - Not found")
            return False
        
        # Test npm
        npm_result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if npm_result.returncode == 0:
            print(f"✅ npm {npm_result.stdout.strip()} - OK")
        else:
            print("❌ npm - Not found")
            return False
        
        return True
    except FileNotFoundError:
        print("❌ Node.js/npm - Not found")
        return False

def test_frontend_dependencies():
    """Test frontend dependencies"""
    print("\n⚛️ Testing frontend dependencies...")
    package_json_path = Path('frontend/package.json')
    node_modules_path = Path('frontend/node_modules')
    
    if not package_json_path.exists():
        print("❌ frontend/package.json - Missing")
        return False
    
    if not node_modules_path.exists():
        print("❌ frontend/node_modules - Missing (run: cd frontend && npm install)")
        return False
    
    print("✅ Frontend dependencies - OK")
    return True

def main():
    """Main test function"""
    print("🧪 AI Academic Chatbot Setup Test")
    print("=" * 50)
    
    tests = [
        test_python_version,
        test_required_packages,
        test_spacy_model,
        test_mongodb_connection,
        test_file_structure,
        test_node_and_npm,
        test_frontend_dependencies
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 All tests passed! ({passed}/{total})")
        print("\n✅ Your setup is ready!")
        print("\nTo start the application:")
        print("1. Backend: cd backend && python run.py")
        print("2. Frontend: cd frontend && npm run dev")
        return True
    else:
        print(f"⚠️  {passed}/{total} tests passed")
        print(f"❌ {total - passed} tests failed")
        print("\n🔧 Please fix the failing tests before running the application.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
